import React, { useState, useEffect } from 'react';

const SMeter = ({ isActive, frequency }) => {
  const [signalLevel, setSignalLevel] = useState(0);
  const [peakLevel, setPeakLevel] = useState(0);
  const [animationId, setAnimationId] = useState(null);

  // Simulation du signal basée sur la fréquence
  useEffect(() => {
    if (isActive) {
      const animate = () => {
        // Simuler un signal variable basé sur la fréquence
        const baseLevel = Math.sin(Date.now() / 1000) * 2 + 3; // Signal de base
        const noise = (Math.random() - 0.5) * 1.5; // Bruit
        const freqFactor = (frequency % 1000000) / 1000000; // Facteur basé sur la fréquence
        
        let newLevel = Math.max(0, Math.min(9, baseLevel + noise + freqFactor * 2));
        
        // Ajouter des pics occasionnels
        if (Math.random() < 0.05) {
          newLevel = Math.min(9, newLevel + Math.random() * 3);
        }
        
        setSignalLevel(newLevel);
        
        // Gestion du pic
        if (newLevel > peakLevel) {
          setPeakLevel(newLevel);
        } else if (peakLevel > 0) {
          setPeakLevel(prev => Math.max(0, prev - 0.02));
        }
        
        const id = requestAnimationFrame(animate);
        setAnimationId(id);
      };
      
      animate();
    } else {
      if (animationId) {
        cancelAnimationFrame(animationId);
        setAnimationId(null);
      }
      setSignalLevel(0);
      setPeakLevel(0);
    }
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [isActive, frequency]);

  // Conversion en dBm
  const getDbm = (level) => {
    return Math.round(-120 + (level * 10));
  };

  // Couleur du segment selon le niveau
  const getSegmentColor = (index, level) => {
    if (index >= level) return 'bg-gray-700 border-gray-600';
    
    if (index < 3) return 'bg-green-400 border-green-300';
    if (index < 6) return 'bg-yellow-400 border-yellow-300';
    return 'bg-red-400 border-red-300';
  };

  // Couleur du pic
  const getPeakColor = (level) => {
    if (level < 3) return 'bg-green-300';
    if (level < 6) return 'bg-yellow-300';
    return 'bg-red-300';
  };

  return (
    <div className="mt-4">
      {/* En-tête du S-meter */}
      <div className="flex justify-between text-sm mb-2">
        <span className="text-green-400">S-METER</span>
        <span className="text-green-400 font-mono">
          {isActive ? `${getDbm(signalLevel)} dBm` : '--- dBm'}
        </span>
      </div>
      
      {/* Échelle S */}
      <div className="flex justify-between text-xs text-gray-400 mb-1">
        <span>1</span>
        <span>3</span>
        <span>5</span>
        <span>7</span>
        <span>9</span>
        <span>+20</span>
        <span>+40</span>
        <span>+60</span>
      </div>
      
      {/* Barres du S-meter */}
      <div className="flex space-x-1 mb-2">
        {Array.from({ length: 9 }, (_, i) => (
          <div
            key={i}
            className={`h-4 flex-1 border transition-all duration-100 ${
              getSegmentColor(i, signalLevel)
            }`}
            style={{
              opacity: i < signalLevel ? 1 : 0.3,
              transform: i < signalLevel ? 'scaleY(1)' : 'scaleY(0.7)'
            }}
          />
        ))}
      </div>
      
      {/* Indicateur de pic */}
      {isActive && peakLevel > 0 && (
        <div className="relative h-1 mb-2">
          <div
            className={`absolute h-1 w-2 ${getPeakColor(peakLevel)} transition-all duration-100`}
            style={{
              left: `${(peakLevel / 9) * 100}%`,
              transform: 'translateX(-50%)'
            }}
          />
        </div>
      )}
      
      {/* Informations détaillées */}
      <div className="flex justify-between text-xs text-gray-400">
        <span>Signal: S{Math.floor(signalLevel) || 0}</span>
        <span>Pic: S{Math.floor(peakLevel) || 0}</span>
        <span>{isActive ? 'ACTIF' : 'INACTIF'}</span>
      </div>
    </div>
  );
};

export default SMeter;
