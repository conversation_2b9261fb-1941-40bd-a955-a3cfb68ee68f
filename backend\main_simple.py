"""
Serveur FastAPI simplifié pour ICOM IC-R8600
Interface simple et robuste pour opérateur radio avec communication réseau RJ45
"""

import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# Import des modules de communication réseau
try:
    from network_handler import ICOMNetworkHandler, NetworkConfig
    from audio_recorder import AudioRecorder
    NETWORK_AVAILABLE = True
except ImportError as e:
    print(f"Modules réseau non disponibles: {e}")
    NETWORK_AVAILABLE = False

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Modèles de données simplifiés
class CommandRequest(BaseModel):
    frequency: Optional[int] = None
    mode: Optional[str] = None
    volume: Optional[int] = None
    squelch: Optional[int] = None
    rf_gain: Optional[int] = None

class AudioRequest(BaseModel):
    audio_type: str = "AF"
    frequency: Optional[int] = None
    record: bool = False

class StatusResponse(BaseModel):
    frequency: int
    mode: str
    volume: int
    squelch: int
    power_on: bool
    signal_level: int
    connection_status: str

# Application FastAPI
app = FastAPI(
    title="ICOM IC-R8600 Simple Controller",
    description="Interface simplifiée pour contrôle ICOM IC-R8600",
    version="1.0.0"
)

# CORS pour frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# État global du récepteur
radio_state = {
    "frequency": 145500000,
    "mode": "FM",
    "volume": 50,
    "squelch": 10,
    "power_on": False,
    "signal_level": 0,
    "connection_status": "disconnected",
    "last_update": None
}

# Configuration réseau
network_config = {
    "host": "*************",
    "port": 50001,
    "protocol": "UDP",
    "timeout": 3.0
}

# Instances globales pour communication réseau
network_handler = None
audio_recorder = None

def load_config():
    """Charger la configuration depuis config.json"""
    global network_handler, audio_recorder

    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
            if 'icom' in config:
                icom_config = config['icom']
                network_config.update({
                    "host": icom_config.get("udp_host", "*************"),
                    "port": icom_config.get("udp_port", 50001),
                    "timeout": icom_config.get("network_timeout", 3.0)
                })

                # Initialiser la communication réseau si disponible
                if NETWORK_AVAILABLE:
                    try:
                        net_config = NetworkConfig(
                            host=network_config["host"],
                            port=network_config["port"],
                            timeout=network_config["timeout"]
                        )
                        network_handler = ICOMNetworkHandler(net_config)
                        logger.info(f"Communication réseau initialisée: {network_config}")

                        # Initialiser l'enregistreur audio
                        audio_recorder = AudioRecorder()
                        logger.info("Enregistreur audio initialisé")

                    except Exception as e:
                        logger.error(f"Erreur initialisation réseau: {e}")
                        network_handler = None
                        audio_recorder = None
                else:
                    logger.warning("Modules réseau non disponibles - Mode simulation")

        logger.info(f"Configuration chargée: {network_config}")
    except Exception as e:
        logger.warning(f"Impossible de charger config.json: {e}")

def send_icom_command(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Envoyer une commande au récepteur ICOM via réseau ou simulation
    """
    global network_handler

    try:
        results = []

        # Utiliser la vraie communication réseau si disponible
        if network_handler and NETWORK_AVAILABLE:
            try:
                # Connecter si pas encore fait
                if not network_handler.is_connected():
                    network_handler.connect()

                # Envoyer les commandes via le handler réseau
                if "frequency" in command:
                    freq = command["frequency"]
                    success = network_handler.set_frequency(freq)
                    if success:
                        radio_state["frequency"] = freq
                        results.append(f"Fréquence {freq} Hz: OK")
                        radio_state["connection_status"] = "connected"
                    else:
                        results.append(f"Fréquence {freq} Hz: ERREUR")
                        radio_state["connection_status"] = "error"

                if "mode" in command:
                    mode = command["mode"]
                    success = network_handler.set_mode(mode)
                    if success:
                        radio_state["mode"] = mode
                        results.append(f"Mode {mode}: OK")
                    else:
                        results.append(f"Mode {mode}: ERREUR")

                if "volume" in command:
                    vol = command["volume"]
                    success = network_handler.set_volume(vol)
                    if success:
                        radio_state["volume"] = vol
                        results.append(f"Volume {vol}%: OK")
                    else:
                        results.append(f"Volume {vol}%: ERREUR")

                if "squelch" in command:
                    sq = command["squelch"]
                    success = network_handler.set_squelch(sq)
                    if success:
                        radio_state["squelch"] = sq
                        results.append(f"Squelch {sq}%: OK")
                    else:
                        results.append(f"Squelch {sq}%: ERREUR")

            except Exception as e:
                logger.error(f"Erreur communication réseau: {e}")
                radio_state["connection_status"] = "error"
                return {
                    "success": False,
                    "message": f"Erreur réseau: {str(e)}",
                    "data": radio_state.copy()
                }

        else:
            # Mode simulation si pas de réseau
            logger.info("Mode simulation - pas de communication réseau")

            if "frequency" in command:
                freq = command["frequency"]
                if 100000 <= freq <= 3000000000:
                    radio_state["frequency"] = freq
                    results.append(f"Fréquence {freq} Hz: OK (simulation)")
                else:
                    results.append(f"Fréquence {freq} Hz: ERREUR - Hors plage")

            if "mode" in command:
                mode = command["mode"]
                valid_modes = ["FM", "AM", "USB", "LSB", "WFM", "CW", "DV"]
                if mode in valid_modes:
                    radio_state["mode"] = mode
                    results.append(f"Mode {mode}: OK (simulation)")
                else:
                    results.append(f"Mode {mode}: ERREUR - Mode invalide")

            if "volume" in command:
                vol = command["volume"]
                if 0 <= vol <= 100:
                    radio_state["volume"] = vol
                    results.append(f"Volume {vol}%: OK (simulation)")
                else:
                    results.append(f"Volume {vol}%: ERREUR - Hors plage")

            if "squelch" in command:
                sq = command["squelch"]
                if 0 <= sq <= 100:
                    radio_state["squelch"] = sq
                    results.append(f"Squelch {sq}%: OK (simulation)")
                else:
                    results.append(f"Squelch {sq}%: ERREUR - Hors plage")

            radio_state["connection_status"] = "connected"

        # Mettre à jour l'état
        radio_state["last_update"] = datetime.now().isoformat()

        return {
            "success": True,
            "message": "; ".join(results) if results else "Aucune commande",
            "data": radio_state.copy()
        }

    except Exception as e:
        logger.error(f"Erreur envoi commande: {e}")
        radio_state["connection_status"] = "error"
        return {
            "success": False,
            "message": f"Erreur: {str(e)}",
            "data": radio_state.copy()
        }

@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage"""
    logger.info("Démarrage du serveur ICOM IC-R8600 Simple")
    load_config()
    
    # Créer le dossier recordings s'il n'existe pas
    os.makedirs("recordings", exist_ok=True)

@app.get("/")
async def root():
    """Page d'accueil de l'API"""
    return {
        "message": "ICOM IC-R8600 Simple Controller API",
        "version": "1.0.0",
        "status": "running",
        "radio_state": radio_state
    }

@app.post("/api/command")
async def send_command(command: CommandRequest):
    """Envoyer une commande au récepteur"""
    try:
        logger.info(f"Commande reçue: {command.dict(exclude_none=True)}")
        
        # Convertir en dictionnaire sans valeurs None
        cmd_dict = command.dict(exclude_none=True)
        
        if not cmd_dict:
            raise HTTPException(status_code=400, detail="Aucune commande fournie")
        
        # Envoyer la commande au récepteur
        result = send_icom_command(cmd_dict)
        
        logger.info(f"Résultat commande: {result['message']}")
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"Erreur traitement commande: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"Erreur serveur: {str(e)}",
                "data": radio_state.copy()
            }
        )

@app.get("/api/status")
async def get_status():
    """Obtenir l'état actuel du récepteur"""
    try:
        return StatusResponse(
            frequency=radio_state["frequency"],
            mode=radio_state["mode"],
            volume=radio_state["volume"],
            squelch=radio_state["squelch"],
            power_on=radio_state["power_on"],
            signal_level=radio_state["signal_level"],
            connection_status=radio_state["connection_status"]
        )
    except Exception as e:
        logger.error(f"Erreur lecture statut: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/power")
async def toggle_power(power_on: bool):
    """Allumer/éteindre le récepteur"""
    try:
        radio_state["power_on"] = power_on
        radio_state["last_update"] = datetime.now().isoformat()
        
        if power_on:
            radio_state["connection_status"] = "connected"
            logger.info("Récepteur allumé")
        else:
            radio_state["connection_status"] = "disconnected"
            radio_state["signal_level"] = 0
            logger.info("Récepteur éteint")
        
        return {
            "success": True,
            "message": f"Récepteur {'allumé' if power_on else 'éteint'}",
            "power_on": power_on
        }
        
    except Exception as e:
        logger.error(f"Erreur changement alimentation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/audio/start")
async def start_audio(request: AudioRequest):
    """Démarrer l'écoute ou l'enregistrement audio"""
    global audio_recorder

    try:
        logger.info(f"Démarrage audio: {request.dict()}")

        if not radio_state["power_on"]:
            raise HTTPException(status_code=400, detail="Récepteur éteint")

        # Utiliser le vrai enregistreur audio si disponible
        if audio_recorder and NETWORK_AVAILABLE and request.record:
            try:
                filename = audio_recorder.start_recording(
                    audio_type=request.audio_type,
                    frequency=request.frequency or radio_state["frequency"]
                )
                logger.info(f"Enregistrement démarré: {filename}")

                return {
                    "success": True,
                    "message": f"Enregistrement démarré: {filename}",
                    "frequency": radio_state["frequency"],
                    "mode": radio_state["mode"],
                    "recording": True,
                    "filename": filename
                }
            except Exception as e:
                logger.error(f"Erreur enregistrement audio: {e}")
                # Continuer en mode simulation

        # Mode simulation ou écoute simple
        action = "enregistrement" if request.record else "écoute"

        return {
            "success": True,
            "message": f"Démarrage {action} sur {radio_state['frequency']} Hz",
            "frequency": radio_state["frequency"],
            "mode": radio_state["mode"],
            "recording": request.record
        }

    except Exception as e:
        logger.error(f"Erreur démarrage audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/audio/stop")
async def stop_audio():
    """Arrêter l'écoute ou l'enregistrement audio"""
    global audio_recorder

    try:
        logger.info("Arrêt audio")

        # Arrêter l'enregistrement si en cours
        if audio_recorder and NETWORK_AVAILABLE:
            try:
                filename = audio_recorder.stop_recording()
                if filename:
                    logger.info(f"Enregistrement arrêté: {filename}")
                    return {
                        "success": True,
                        "message": f"Enregistrement arrêté: {filename}",
                        "timestamp": datetime.now().isoformat(),
                        "filename": filename
                    }
            except Exception as e:
                logger.error(f"Erreur arrêt enregistrement: {e}")

        return {
            "success": True,
            "message": "Audio arrêté",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Erreur arrêt audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/recordings")
async def list_recordings():
    """Lister les enregistrements disponibles"""
    try:
        recordings_dir = "recordings"
        recordings = []
        
        if os.path.exists(recordings_dir):
            for filename in os.listdir(recordings_dir):
                if filename.endswith(('.wav', '.mp3')):
                    filepath = os.path.join(recordings_dir, filename)
                    stat = os.stat(filepath)
                    recordings.append({
                        "filename": filename,
                        "size": stat.st_size,
                        "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "duration": 0  # À calculer si nécessaire
                    })
        
        return recordings
        
    except Exception as e:
        logger.error(f"Erreur liste enregistrements: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Vérification de santé du service"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "radio_connected": radio_state["connection_status"] == "connected",
        "power_on": radio_state["power_on"]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
