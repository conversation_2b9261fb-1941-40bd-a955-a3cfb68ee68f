#!/usr/bin/env python3
"""
Test de communication CI-V avec ICOM IC-R8600
Outil de diagnostic pour vérifier la configuration réseau
"""

import socket
import time
import sys

# Configuration
ICOM_IP = "**************"
ICOM_PORT = 50001
ICOM_ADDRESS = 0xDF  # USB/LAN Remote address

def test_udp_connection():
    """Test de base de la connexion UDP"""
    print(f"🔍 Test connexion UDP vers {ICOM_IP}:{ICOM_PORT}")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(2.0)
        
        # Test de connexion basique
        test_data = b"TEST"
        sock.sendto(test_data, (ICOM_IP, ICOM_PORT))
        print("✅ Envoi UDP réussi")
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ Réponse reçue: {response.hex()}")
        except socket.timeout:
            print("⚠️  Pas de réponse (timeout)")
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur connexion UDP: {e}")
        return False

def test_civ_ping():
    """Test ping CI-V basique"""
    print(f"\n🔍 Test ping CI-V (adresse {ICOM_ADDRESS:02X}h)")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Commande CI-V ping: FE FE E0 DF FD
        ping_command = bytes([0xFE, 0xFE, 0xE0, ICOM_ADDRESS, 0xFD])
        print(f"📤 Envoi: {ping_command.hex().upper()}")
        
        sock.sendto(ping_command, (ICOM_IP, ICOM_PORT))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"📥 Réponse: {response.hex().upper()}")
            
            if len(response) >= 6 and response[0:2] == b'\xFE\xFE':
                print("✅ Réponse CI-V valide reçue!")
                return True
            else:
                print("⚠️  Réponse non-CI-V")
                return False
                
        except socket.timeout:
            print("❌ Timeout - Pas de réponse CI-V")
            return False
        
    except Exception as e:
        print(f"❌ Erreur ping CI-V: {e}")
        return False
    finally:
        sock.close()

def test_frequency_command():
    """Test commande de fréquence"""
    print(f"\n🔍 Test commande fréquence (145.500 MHz)")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Commande fréquence 145.500 MHz: FE FE E0 DF 05 00 50 45 01 FD
        freq_command = bytes([0xFE, 0xFE, 0xE0, ICOM_ADDRESS, 0x05, 0x00, 0x50, 0x45, 0x01, 0xFD])
        print(f"📤 Envoi: {freq_command.hex().upper()}")
        
        sock.sendto(freq_command, (ICOM_IP, ICOM_PORT))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"📥 Réponse: {response.hex().upper()}")
            
            if response == bytes([0xFE, 0xFE, 0xE0, ICOM_ADDRESS, 0xFB, 0xFD]):
                print("✅ Commande acceptée (FB)")
                return True
            elif response == bytes([0xFE, 0xFE, 0xE0, ICOM_ADDRESS, 0xFA, 0xFD]):
                print("❌ Commande rejetée (FA)")
                return False
            else:
                print("⚠️  Réponse inattendue")
                return False
                
        except socket.timeout:
            print("❌ Timeout - Pas de réponse")
            return False
        
    except Exception as e:
        print(f"❌ Erreur commande fréquence: {e}")
        return False
    finally:
        sock.close()

def test_different_addresses():
    """Test avec différentes adresses CI-V"""
    print(f"\n🔍 Test adresses CI-V multiples")
    
    addresses = [
        (0x96, "96h - CI-V standard"),
        (0xDF, "DFh - USB/LAN Remote"),
        (0x00, "00h - Broadcast")
    ]
    
    for addr, desc in addresses:
        print(f"\n📡 Test adresse {desc}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(2.0)
            
            ping_command = bytes([0xFE, 0xFE, 0xE0, addr, 0xFD])
            print(f"📤 Envoi: {ping_command.hex().upper()}")
            
            sock.sendto(ping_command, (ICOM_IP, ICOM_PORT))
            
            try:
                response, addr_resp = sock.recvfrom(1024)
                print(f"📥 Réponse: {response.hex().upper()}")
                print(f"✅ Adresse {desc} répond!")
            except socket.timeout:
                print(f"❌ Pas de réponse pour {desc}")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Erreur test {desc}: {e}")

def main():
    """Fonction principale de diagnostic"""
    print("=" * 60)
    print("🔧 DIAGNOSTIC CI-V ICOM IC-R8600")
    print("=" * 60)
    
    print(f"📍 Cible: {ICOM_IP}:{ICOM_PORT}")
    print(f"📍 Adresse CI-V: {ICOM_ADDRESS:02X}h")
    
    # Tests séquentiels
    udp_ok = test_udp_connection()
    
    if udp_ok:
        ping_ok = test_civ_ping()
        
        if ping_ok:
            freq_ok = test_frequency_command()
        else:
            print("\n🔍 Test adresses alternatives...")
            test_different_addresses()
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DU DIAGNOSTIC")
    print("=" * 60)
    
    if not udp_ok:
        print("❌ Problème réseau de base")
        print("   → Vérifiez l'IP et le port du récepteur")
        print("   → Vérifiez que Network Control est ON")
    else:
        print("✅ Connexion UDP fonctionnelle")
        
        print("\n🔧 ACTIONS RECOMMANDÉES:")
        print("1. Sur le récepteur ICOM IC-R8600:")
        print("   → Menu → CI-V → CI-V Enable = ON")
        print("   → Menu → Network → CI-V over Network = ON")
        print("   → Menu → Network → Control Port UDP = 50001")
        print("   → Redémarrer le récepteur")
        
        print("\n2. Vérifiez les adresses CI-V:")
        print("   → CI-V Address (pour USB/série)")
        print("   → USB/LAN Remote Transceive Address (pour réseau)")

if __name__ == "__main__":
    main()
