"""
Système de monitoring et logging pour ICOM R8600
Surveillance en temps réel et traçabilité complète
"""

import asyncio
import logging
import time
import psutil
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import threading
from collections import deque, defaultdict

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class ComponentType(Enum):
    RADIO = "radio"
    NETWORK = "network"
    AUDIO = "audio"
    DATABASE = "database"
    WEBSOCKET = "websocket"
    SYSTEM = "system"

@dataclass
class MonitoringEvent:
    timestamp: float
    component: str
    event_type: str
    level: str
    message: str
    data: Dict[str, Any] = None
    duration_ms: Optional[float] = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}

@dataclass
class PerformanceMetrics:
    timestamp: float
    cpu_percent: float
    memory_percent: float
    network_latency_ms: float
    radio_commands_per_sec: float
    websocket_connections: int
    error_rate: float
    uptime_seconds: float

class MonitoringSystem:
    """Système de monitoring complet pour ICOM R8600"""
    
    def __init__(self, max_events: int = 10000, log_file: str = "monitoring.log"):
        self.max_events = max_events
        self.log_file = Path(log_file)
        
        # Stockage des événements en mémoire
        self.events = deque(maxlen=max_events)
        self.metrics_history = deque(maxlen=1440)  # 24h à 1 minute d'intervalle
        
        # Compteurs et statistiques
        self.counters = defaultdict(int)
        self.timers = defaultdict(list)
        self.alerts = deque(maxlen=100)
        
        # État du système
        self.start_time = time.time()
        self.is_monitoring = False
        self.monitoring_thread = None
        
        # Callbacks pour notifications
        self.alert_callbacks: List[Callable] = []
        self.metric_callbacks: List[Callable] = []
        
        # Configuration des seuils d'alerte
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'network_latency_ms': 100.0,
            'error_rate': 5.0,  # 5% d'erreurs
            'response_time_ms': 200.0
        }
        
        # Logger
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """Configure le logger pour le monitoring"""
        logger = logging.getLogger('icom_monitoring')
        logger.setLevel(logging.DEBUG)
        
        # Handler pour fichier
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setLevel(logging.INFO)
        
        # Handler pour console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        
        # Format des logs
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def start_monitoring(self):
        """Démarre le système de monitoring"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_worker, daemon=True)
        self.monitoring_thread.start()
        
        self.log_event(
            ComponentType.SYSTEM, 
            "monitoring_started", 
            LogLevel.INFO,
            "Système de monitoring démarré"
        )
    
    def stop_monitoring(self):
        """Arrête le système de monitoring"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5.0)
        
        self.log_event(
            ComponentType.SYSTEM, 
            "monitoring_stopped", 
            LogLevel.INFO,
            "Système de monitoring arrêté"
        )
    
    def _monitoring_worker(self):
        """Worker thread pour collecte des métriques"""
        while self.is_monitoring:
            try:
                # Collecter les métriques système
                metrics = self._collect_system_metrics()
                self.metrics_history.append(metrics)
                
                # Vérifier les seuils d'alerte
                self._check_alerts(metrics)
                
                # Notifier les callbacks
                for callback in self.metric_callbacks:
                    try:
                        callback(metrics)
                    except Exception as e:
                        self.logger.error(f"Erreur callback métrique: {e}")
                
                # Attendre 60 secondes
                time.sleep(60)
                
            except Exception as e:
                self.logger.error(f"Erreur monitoring worker: {e}")
                time.sleep(10)
    
    def _collect_system_metrics(self) -> PerformanceMetrics:
        """Collecte les métriques système"""
        try:
            # Métriques système
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Métriques réseau (simulées pour l'exemple)
            network_latency = self._calculate_average_latency()
            
            # Métriques application
            radio_commands_rate = self._calculate_command_rate()
            websocket_connections = self.counters.get('websocket_connections', 0)
            error_rate = self._calculate_error_rate()
            
            # Uptime
            uptime = time.time() - self.start_time
            
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                network_latency_ms=network_latency,
                radio_commands_per_sec=radio_commands_rate,
                websocket_connections=websocket_connections,
                error_rate=error_rate,
                uptime_seconds=uptime
            )
            
        except Exception as e:
            self.logger.error(f"Erreur collecte métriques: {e}")
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=0,
                memory_percent=0,
                network_latency_ms=0,
                radio_commands_per_sec=0,
                websocket_connections=0,
                error_rate=0,
                uptime_seconds=0
            )
    
    def _calculate_average_latency(self) -> float:
        """Calcule la latence moyenne des commandes"""
        latencies = self.timers.get('command_latency', [])
        if not latencies:
            return 0.0
        
        # Garder seulement les 100 dernières mesures
        recent_latencies = latencies[-100:]
        return sum(recent_latencies) / len(recent_latencies)
    
    def _calculate_command_rate(self) -> float:
        """Calcule le taux de commandes par seconde"""
        now = time.time()
        minute_ago = now - 60
        
        # Compter les événements de commande dans la dernière minute
        command_count = sum(1 for event in self.events 
                          if event.timestamp > minute_ago 
                          and event.event_type == 'command_sent')
        
        return command_count / 60.0
    
    def _calculate_error_rate(self) -> float:
        """Calcule le taux d'erreur en pourcentage"""
        now = time.time()
        minute_ago = now - 60
        
        total_events = sum(1 for event in self.events 
                          if event.timestamp > minute_ago)
        
        error_events = sum(1 for event in self.events 
                          if event.timestamp > minute_ago 
                          and event.level in ['ERROR', 'CRITICAL'])
        
        if total_events == 0:
            return 0.0
        
        return (error_events / total_events) * 100.0
    
    def _check_alerts(self, metrics: PerformanceMetrics):
        """Vérifie les seuils d'alerte"""
        alerts = []
        
        if metrics.cpu_percent > self.alert_thresholds['cpu_percent']:
            alerts.append(f"CPU élevé: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.alert_thresholds['memory_percent']:
            alerts.append(f"Mémoire élevée: {metrics.memory_percent:.1f}%")
        
        if metrics.network_latency_ms > self.alert_thresholds['network_latency_ms']:
            alerts.append(f"Latence élevée: {metrics.network_latency_ms:.1f}ms")
        
        if metrics.error_rate > self.alert_thresholds['error_rate']:
            alerts.append(f"Taux d'erreur élevé: {metrics.error_rate:.1f}%")
        
        # Enregistrer les alertes
        for alert_msg in alerts:
            self._trigger_alert(alert_msg, 'performance')
    
    def _trigger_alert(self, message: str, alert_type: str):
        """Déclenche une alerte"""
        alert = {
            'timestamp': time.time(),
            'type': alert_type,
            'message': message,
            'level': 'WARNING'
        }
        
        self.alerts.append(alert)
        self.logger.warning(f"ALERTE: {message}")
        
        # Notifier les callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"Erreur callback alerte: {e}")
    
    def log_event(self, component: ComponentType, event_type: str, 
                  level: LogLevel, message: str, data: Dict[str, Any] = None,
                  duration_ms: Optional[float] = None):
        """Enregistre un événement"""
        event = MonitoringEvent(
            timestamp=time.time(),
            component=component.value,
            event_type=event_type,
            level=level.value,
            message=message,
            data=data or {},
            duration_ms=duration_ms
        )
        
        self.events.append(event)
        
        # Logger selon le niveau
        log_msg = f"[{component.value}] {event_type}: {message}"
        if data:
            log_msg += f" | Data: {json.dumps(data)}"
        
        if level == LogLevel.DEBUG:
            self.logger.debug(log_msg)
        elif level == LogLevel.INFO:
            self.logger.info(log_msg)
        elif level == LogLevel.WARNING:
            self.logger.warning(log_msg)
        elif level == LogLevel.ERROR:
            self.logger.error(log_msg)
        elif level == LogLevel.CRITICAL:
            self.logger.critical(log_msg)
    
    def increment_counter(self, name: str, value: int = 1):
        """Incrémente un compteur"""
        self.counters[name] += value
    
    def record_timing(self, name: str, duration_ms: float):
        """Enregistre une mesure de temps"""
        self.timers[name].append(duration_ms)
        
        # Garder seulement les 1000 dernières mesures
        if len(self.timers[name]) > 1000:
            self.timers[name] = self.timers[name][-1000:]
    
    def get_events(self, component: Optional[str] = None, 
                   level: Optional[str] = None,
                   hours_back: int = 24) -> List[Dict[str, Any]]:
        """Récupère les événements filtrés"""
        cutoff_time = time.time() - (hours_back * 3600)
        
        filtered_events = []
        for event in self.events:
            if event.timestamp < cutoff_time:
                continue
            
            if component and event.component != component:
                continue
            
            if level and event.level != level:
                continue
            
            filtered_events.append(asdict(event))
        
        return sorted(filtered_events, key=lambda x: x['timestamp'], reverse=True)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Retourne un résumé des métriques"""
        if not self.metrics_history:
            return {}
        
        latest = self.metrics_history[-1]
        
        # Calculer les moyennes sur la dernière heure
        hour_ago = time.time() - 3600
        recent_metrics = [m for m in self.metrics_history if m.timestamp > hour_ago]
        
        if recent_metrics:
            avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
            avg_latency = sum(m.network_latency_ms for m in recent_metrics) / len(recent_metrics)
        else:
            avg_cpu = avg_memory = avg_latency = 0
        
        return {
            'current': asdict(latest),
            'averages_1h': {
                'cpu_percent': round(avg_cpu, 1),
                'memory_percent': round(avg_memory, 1),
                'network_latency_ms': round(avg_latency, 1)
            },
            'counters': dict(self.counters),
            'uptime_hours': round((time.time() - self.start_time) / 3600, 1),
            'total_events': len(self.events),
            'active_alerts': len([a for a in self.alerts 
                                if time.time() - a['timestamp'] < 3600])
        }
    
    def register_alert_callback(self, callback: Callable):
        """Enregistre un callback pour les alertes"""
        self.alert_callbacks.append(callback)
    
    def register_metric_callback(self, callback: Callable):
        """Enregistre un callback pour les métriques"""
        self.metric_callbacks.append(callback)
    
    def export_logs(self, filename: str, hours_back: int = 24) -> bool:
        """Exporte les logs vers un fichier"""
        try:
            events = self.get_events(hours_back=hours_back)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'export_timestamp': time.time(),
                    'export_date': datetime.now().isoformat(),
                    'hours_back': hours_back,
                    'total_events': len(events),
                    'events': events,
                    'metrics_summary': self.get_metrics_summary()
                }, f, indent=2, ensure_ascii=False)
            
            self.log_event(
                ComponentType.SYSTEM,
                "logs_exported",
                LogLevel.INFO,
                f"Logs exportés vers {filename}",
                {'event_count': len(events)}
            )
            
            return True
            
        except Exception as e:
            self.log_event(
                ComponentType.SYSTEM,
                "export_error",
                LogLevel.ERROR,
                f"Erreur export logs: {e}"
            )
            return False

# Instance globale du système de monitoring
monitoring_system = MonitoringSystem()

# Décorateur pour mesurer les performances
def monitor_performance(component: ComponentType, event_type: str):
    """Décorateur pour monitorer les performances d'une fonction"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                monitoring_system.log_event(
                    component, event_type, LogLevel.INFO,
                    f"Fonction {func.__name__} exécutée",
                    {'args_count': len(args), 'kwargs_count': len(kwargs)},
                    duration_ms
                )
                
                monitoring_system.record_timing(f"{component.value}_{event_type}", duration_ms)
                monitoring_system.increment_counter(f"{component.value}_calls")
                
                return result
                
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                
                monitoring_system.log_event(
                    component, f"{event_type}_error", LogLevel.ERROR,
                    f"Erreur dans {func.__name__}: {str(e)}",
                    {'error_type': type(e).__name__},
                    duration_ms
                )
                
                monitoring_system.increment_counter(f"{component.value}_errors")
                raise
        
        return wrapper
    return decorator
