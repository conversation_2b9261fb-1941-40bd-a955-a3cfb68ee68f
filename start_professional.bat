@echo off
echo ========================================
echo ICOM IC-R8600 Professional Controller
echo Version 2.0.0 - Interface Professionnelle
echo ========================================
echo.

echo [1/4] Verification de l'environnement Python...
python --version
if %errorlevel% neq 0 (
    echo ERREUR: Python n'est pas installe ou non accessible
    pause
    exit /b 1
)

echo [2/4] Installation/Verification des dependances...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERREUR: Echec installation des dependances
    pause
    exit /b 1
)

echo [3/4] Demarrage du serveur backend professionnel...
cd backend
start "ICOM Backend Professional" python main_professional.py

echo [4/4] Attente du demarrage du serveur...
timeout /t 3 /nobreak > nul

echo.
echo ========================================
echo Serveur professionnel demarre !
echo.
echo URLs disponibles:
echo - API Documentation: http://localhost:8000/docs
echo - Interface principale: http://localhost:8000
echo - WebSocket: ws://localhost:8000/ws
echo - Monitoring: http://localhost:8000/api/monitoring/metrics
echo.
echo Fonctionnalites activees:
echo [x] Communication ultra-rapide (latence ^< 50ms)
echo [x] WebSocket temps reel
echo [x] Base de donnees SQLite
echo [x] Monitoring systeme
echo [x] Interface professionnelle
echo [x] Streaming audio
echo [x] Logs detailles
echo.
echo Appuyez sur une touche pour ouvrir l'interface...
pause > nul

echo Ouverture de l'interface web...
start http://localhost:8000

echo.
echo Le serveur fonctionne en arriere-plan.
echo Fermez cette fenetre pour arreter le serveur.
pause
