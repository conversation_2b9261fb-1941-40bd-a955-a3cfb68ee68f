import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Volume2, VolumeX, Mic, MicOff, Play, Pause, Square, 
  Radio, Signal, Headphones, Settings, Download,
  BarChart3, Waves, Activity
} from 'lucide-react';

const AudioControls = ({ 
  radioStatus = {}, 
  onVolumeChange, 
  onSquelchChange,
  onAudioCommand,
  isConnected = false,
  className = ""
}) => {
  // États locaux
  const [volume, setVolume] = useState(radioStatus.volume || 50);
  const [squelch, setSquelch] = useState(radioStatus.squelch || 0);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [streamQuality, setStreamQuality] = useState('high');
  
  // Références pour l'audio
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const streamRef = useRef(null);
  const animationRef = useRef(null);

  // Initialisation du contexte audio
  useEffect(() => {
    if (typeof window !== 'undefined' && window.AudioContext) {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
    }
    
    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // Analyse du niveau audio en temps réel
  const analyzeAudio = useCallback(() => {
    if (!analyserRef.current) return;
    
    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    analyserRef.current.getByteFrequencyData(dataArray);
    
    // Calculer le niveau moyen
    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
    setAudioLevel(Math.round((average / 255) * 100));
    
    if (isStreaming) {
      animationRef.current = requestAnimationFrame(analyzeAudio);
    }
  }, [isStreaming]);

  // Gestion du volume
  const handleVolumeChange = (newVolume) => {
    setVolume(newVolume);
    if (onVolumeChange) {
      onVolumeChange(newVolume);
    }
  };

  // Gestion du squelch
  const handleSquelchChange = (newSquelch) => {
    setSquelch(newSquelch);
    if (onSquelchChange) {
      onSquelchChange(newSquelch);
    }
  };

  // Démarrage du streaming audio
  const startAudioStream = async () => {
    try {
      if (!isConnected) {
        throw new Error('Pas de connexion au récepteur');
      }

      // Simuler le démarrage du stream (à remplacer par l'API réelle)
      setIsStreaming(true);
      
      // Démarrer l'analyse audio
      if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }
      
      analyzeAudio();
      
      if (onAudioCommand) {
        onAudioCommand('start_stream', { quality: streamQuality });
      }
      
    } catch (error) {
      console.error('Erreur démarrage stream:', error);
      alert(`Erreur: ${error.message}`);
    }
  };

  // Arrêt du streaming audio
  const stopAudioStream = () => {
    setIsStreaming(false);
    setAudioLevel(0);
    
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    
    if (onAudioCommand) {
      onAudioCommand('stop_stream');
    }
  };

  // Démarrage de l'enregistrement
  const startRecording = () => {
    setIsRecording(true);
    if (onAudioCommand) {
      onAudioCommand('start_recording', { 
        format: 'wav',
        quality: streamQuality 
      });
    }
  };

  // Arrêt de l'enregistrement
  const stopRecording = () => {
    setIsRecording(false);
    if (onAudioCommand) {
      onAudioCommand('stop_recording');
    }
  };

  // Basculer le mute
  const toggleMute = () => {
    setIsMuted(!isMuted);
    if (onAudioCommand) {
      onAudioCommand('mute', { muted: !isMuted });
    }
  };

  // Visualiseur de niveau audio
  const AudioLevelMeter = () => (
    <div className="flex items-center space-x-2">
      <Activity size={16} className="text-green-500" />
      <div className="flex space-x-1">
        {Array.from({ length: 10 }, (_, i) => (
          <div
            key={i}
            className={`w-2 h-6 rounded-sm ${
              i < (audioLevel / 10) 
                ? i < 7 ? 'bg-green-500' : i < 9 ? 'bg-yellow-500' : 'bg-red-500'
                : 'bg-gray-300'
            }`}
          />
        ))}
      </div>
      <span className="text-sm font-mono">{audioLevel}%</span>
    </div>
  );

  return (
    <div className={`audio-controls bg-white rounded-lg shadow-lg p-6 ${className}`}>
      {/* En-tête */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-gray-800 flex items-center">
          <Headphones size={24} className="mr-2 text-blue-600" />
          Contrôles Audio
        </h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {isConnected ? 'Connecté' : 'Déconnecté'}
        </div>
      </div>

      {/* Contrôles principaux */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        
        {/* Volume */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <Volume2 size={16} className="mr-2" />
              Volume
            </label>
            <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
              {volume}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={volume}
            onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
            disabled={!isConnected}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>0%</span>
            <span>50%</span>
            <span>100%</span>
          </div>
        </div>

        {/* Squelch */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <Signal size={16} className="mr-2" />
              Squelch
            </label>
            <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
              {squelch}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={squelch}
            onChange={(e) => handleSquelchChange(parseInt(e.target.value))}
            disabled={!isConnected}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>Fermé</span>
            <span>Moyen</span>
            <span>Ouvert</span>
          </div>
        </div>
      </div>

      {/* Streaming et enregistrement */}
      <div className="border-t pt-6 mb-6">
        <h4 className="text-lg font-semibold mb-4 flex items-center">
          <Radio size={20} className="mr-2 text-purple-600" />
          Audio Live
        </h4>
        
        {/* Qualité du stream */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Qualité du stream
          </label>
          <select
            value={streamQuality}
            onChange={(e) => setStreamQuality(e.target.value)}
            disabled={isStreaming || !isConnected}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="low">Basse (8 kHz)</option>
            <option value="medium">Moyenne (16 kHz)</option>
            <option value="high">Haute (48 kHz)</option>
          </select>
        </div>

        {/* Boutons de contrôle */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
          {/* Stream */}
          <button
            onClick={isStreaming ? stopAudioStream : startAudioStream}
            disabled={!isConnected}
            className={`flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors ${
              isStreaming
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-green-500 hover:bg-green-600 text-white'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {isStreaming ? <Pause size={16} className="mr-2" /> : <Play size={16} className="mr-2" />}
            {isStreaming ? 'Stop' : 'Stream'}
          </button>

          {/* Enregistrement */}
          <button
            onClick={isRecording ? stopRecording : startRecording}
            disabled={!isConnected}
            className={`flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors ${
              isRecording
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {isRecording ? <Square size={16} className="mr-2" /> : <Mic size={16} className="mr-2" />}
            {isRecording ? 'Stop' : 'Rec'}
          </button>

          {/* Mute */}
          <button
            onClick={toggleMute}
            disabled={!isConnected}
            className={`flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors ${
              isMuted
                ? 'bg-gray-500 hover:bg-gray-600 text-white'
                : 'bg-yellow-500 hover:bg-yellow-600 text-white'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {isMuted ? <VolumeX size={16} className="mr-2" /> : <Volume2 size={16} className="mr-2" />}
            {isMuted ? 'Unmute' : 'Mute'}
          </button>

          {/* Paramètres */}
          <button
            disabled={!isConnected}
            className="flex items-center justify-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Settings size={16} className="mr-2" />
            Config
          </button>
        </div>

        {/* Visualiseur de niveau audio */}
        {isStreaming && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Niveau Audio</span>
              <span className={`text-sm font-medium ${
                audioLevel > 80 ? 'text-red-600' : audioLevel > 50 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {audioLevel > 80 ? 'FORT' : audioLevel > 20 ? 'MOYEN' : 'FAIBLE'}
              </span>
            </div>
            <AudioLevelMeter />
          </div>
        )}
      </div>

      {/* Indicateurs d'état */}
      <div className="border-t pt-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div className={`p-3 rounded-lg ${isStreaming ? 'bg-green-100' : 'bg-gray-100'}`}>
            <Waves size={20} className={`mx-auto mb-1 ${isStreaming ? 'text-green-600' : 'text-gray-400'}`} />
            <div className="text-xs font-medium">Streaming</div>
            <div className={`text-xs ${isStreaming ? 'text-green-600' : 'text-gray-500'}`}>
              {isStreaming ? 'Actif' : 'Inactif'}
            </div>
          </div>

          <div className={`p-3 rounded-lg ${isRecording ? 'bg-red-100' : 'bg-gray-100'}`}>
            <Mic size={20} className={`mx-auto mb-1 ${isRecording ? 'text-red-600' : 'text-gray-400'}`} />
            <div className="text-xs font-medium">Enregistrement</div>
            <div className={`text-xs ${isRecording ? 'text-red-600' : 'text-gray-500'}`}>
              {isRecording ? 'En cours' : 'Arrêté'}
            </div>
          </div>

          <div className={`p-3 rounded-lg ${isMuted ? 'bg-yellow-100' : 'bg-gray-100'}`}>
            <VolumeX size={20} className={`mx-auto mb-1 ${isMuted ? 'text-yellow-600' : 'text-gray-400'}`} />
            <div className="text-xs font-medium">Mute</div>
            <div className={`text-xs ${isMuted ? 'text-yellow-600' : 'text-gray-500'}`}>
              {isMuted ? 'Activé' : 'Désactivé'}
            </div>
          </div>

          <div className={`p-3 rounded-lg ${isConnected ? 'bg-blue-100' : 'bg-gray-100'}`}>
            <Radio size={20} className={`mx-auto mb-1 ${isConnected ? 'text-blue-600' : 'text-gray-400'}`} />
            <div className="text-xs font-medium">Connexion</div>
            <div className={`text-xs ${isConnected ? 'text-blue-600' : 'text-gray-500'}`}>
              {isConnected ? 'OK' : 'Erreur'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioControls;
