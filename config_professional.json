{"application": {"name": "ICOM IC-R8600 Professional Controller", "version": "2.0.0", "description": "Interface professionnelle pour contrôle ICOM IC-R8600 avec monitoring temps réel"}, "icom": {"connection_type": "network", "network_protocol": "udp", "host": "*************", "port": 50001, "timeout_ms": 20, "max_retries": 5, "retry_delay_ms": 500, "address": "0x96", "controller_address": "0xE0", "tcp_nodelay": true, "tcp_keepalive": true, "fallback_to_serial": true, "serial_port": "COM6", "baudrate": 19200}, "audio": {"sample_rate": 48000, "channels": 1, "bit_depth": 16, "recordings_dir": "recordings", "streaming_enabled": true, "streaming_quality": "high", "buffer_size": 4096, "default_device": null, "formats": ["wav", "mp3", "flac"]}, "api": {"host": "0.0.0.0", "port": 8000, "cors_origins": ["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"], "max_request_size": "10MB", "rate_limiting": {"enabled": true, "requests_per_minute": 1000}}, "websocket": {"max_connections": 50, "heartbeat_interval_seconds": 30, "message_queue_size": 1000, "compression": true, "auto_reconnect": true}, "database": {"type": "sqlite", "path": "icom_r8600_professional.db", "backup_enabled": true, "backup_interval_hours": 24, "cleanup_days": 30, "vacuum_on_startup": true}, "monitoring": {"enabled": true, "log_file": "monitoring.log", "max_events": 10000, "metrics_retention_hours": 24, "alert_thresholds": {"cpu_percent": 80.0, "memory_percent": 85.0, "network_latency_ms": 100.0, "error_rate_percent": 5.0, "response_time_ms": 200.0}, "export_enabled": true, "real_time_alerts": true}, "frontend": {"api_base_url": "http://localhost:8000", "websocket_url": "ws://localhost:8000/ws", "polling_interval_ms": 2000, "theme": "dark", "language": "fr", "auto_connect": true, "notifications_enabled": true}, "presets": {"frequencies": [{"name": "2m FM Repeater", "frequency": 145500000, "mode": "FM", "description": "Répéteur amateur 2m", "category": "Amateur"}, {"name": "70cm FM Repeater", "frequency": 433500000, "mode": "FM", "description": "Répéteur amateur 70cm", "category": "Amateur"}, {"name": "Aviation Control", "frequency": 121500000, "mode": "AM", "description": "Contrôle aérien", "category": "Aviation"}, {"name": "Marine VHF Ch16", "frequency": 156800000, "mode": "FM", "description": "Canal d'urgence maritime", "category": "Marine"}, {"name": "PMR446 Ch1", "frequency": 446006250, "mode": "FM", "description": "PMR446 Canal 1", "category": "PMR"}, {"name": "Police Locale", "frequency": 162000000, "mode": "FM", "description": "Fréquence police locale", "category": "Sécurité"}, {"name": "Pompiers", "frequency": 161000000, "mode": "FM", "description": "<PERSON><PERSON><PERSON> pompiers", "category": "Sécurité"}, {"name": "Météo Marine", "frequency": 162550000, "mode": "FM", "description": "Bulletin météo maritime", "category": "<PERSON><PERSON><PERSON><PERSON>"}], "scan_ranges": [{"name": "Bande Amateur 2m", "start": 144000000, "end": 146000000, "step": 25000, "mode": "FM", "category": "Amateur"}, {"name": "Bande Amateur 70cm", "start": 430000000, "end": 440000000, "step": 25000, "mode": "FM", "category": "Amateur"}, {"name": "Aviation Civile", "start": 118000000, "end": 137000000, "step": 25000, "mode": "AM", "category": "Aviation"}, {"name": "Marine VHF", "start": 156000000, "end": 163000000, "step": 25000, "mode": "FM", "category": "Marine"}, {"name": "PMR446", "start": 446000000, "end": 446200000, "step": 6250, "mode": "FM", "category": "PMR"}]}, "security": {"api_key_required": false, "rate_limiting": true, "cors_strict": false, "log_all_requests": true, "sanitize_inputs": true}, "performance": {"async_workers": 4, "connection_pool_size": 20, "cache_enabled": true, "cache_ttl_seconds": 300, "compression_enabled": true, "optimize_for_latency": true}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_enabled": true, "file_path": "icom_professional.log", "file_max_size_mb": 100, "file_backup_count": 5, "console_enabled": true, "structured_logging": true}}