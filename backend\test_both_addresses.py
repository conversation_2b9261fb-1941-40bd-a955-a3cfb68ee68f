#!/usr/bin/env python3
"""
Test spécifique des deux adresses CI-V de l'ICOM IC-R8600
- CI-V Address: 96H (pour USB/série)
- USB/LAN Remote Transceive Address: DFH (pour réseau)
"""

import socket
import time
import sys

# Configuration
ICOM_IP = "**************"
ICOM_PORT = 50001

def test_address(address, description):
    """Test une adresse CI-V spécifique"""
    print(f"\n🔍 Test adresse {address:02X}h - {description}")
    print("=" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Test 1: Ping CI-V
        ping_command = bytes([0xFE, 0xFE, 0xE0, address, 0xFD])
        print(f"📤 Ping: {ping_command.hex().upper()}")
        
        sock.sendto(ping_command, (ICOM_IP, ICOM_PORT))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"📥 Réponse: {response.hex().upper()}")
            
            if len(response) >= 6 and response[0:2] == b'\xFE\xFE':
                print(f"✅ Adresse {address:02X}h répond au ping!")
                
                # Test 2: Commande Get Frequency
                print(f"\n📡 Test Get Frequency sur {address:02X}h")
                get_freq_cmd = bytes([0xFE, 0xFE, 0xE0, address, 0x03, 0xFD])
                print(f"📤 Get Freq: {get_freq_cmd.hex().upper()}")
                
                sock.sendto(get_freq_cmd, (ICOM_IP, ICOM_PORT))
                
                try:
                    response2, addr2 = sock.recvfrom(1024)
                    print(f"📥 Réponse: {response2.hex().upper()}")
                    
                    if len(response2) >= 11 and response2[4] == 0x03:
                        freq_bytes = response2[5:10]
                        print(f"✅ Fréquence reçue: {freq_bytes.hex().upper()}")
                        return True
                    else:
                        print(f"⚠️  Réponse Get Frequency inattendue")
                        
                except socket.timeout:
                    print(f"❌ Timeout Get Frequency sur {address:02X}h")
                
                return True
            else:
                print(f"⚠️  Réponse non-CI-V sur {address:02X}h")
                return False
                
        except socket.timeout:
            print(f"❌ Timeout ping sur {address:02X}h")
            return False
        
    except Exception as e:
        print(f"❌ Erreur test {address:02X}h: {e}")
        return False
    finally:
        sock.close()

def test_frequency_command(address, description):
    """Test commande de fréquence sur une adresse"""
    print(f"\n🎯 Test Set Frequency sur {address:02X}h - {description}")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Commande fréquence 145.500 MHz: FE FE E0 XX 05 00 50 45 01 FD
        freq_command = bytes([0xFE, 0xFE, 0xE0, address, 0x05, 0x00, 0x50, 0x45, 0x01, 0xFD])
        print(f"📤 Set 145.500 MHz: {freq_command.hex().upper()}")
        
        sock.sendto(freq_command, (ICOM_IP, ICOM_PORT))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"📥 Réponse: {response.hex().upper()}")
            
            # Vérifier ACK (FB) ou NAK (FA)
            if len(response) >= 6:
                if response[4] == 0xFB:
                    print(f"✅ Commande acceptée (FB) sur {address:02X}h")
                    return True
                elif response[4] == 0xFA:
                    print(f"❌ Commande rejetée (FA) sur {address:02X}h")
                    return False
                else:
                    print(f"⚠️  Réponse inattendue: {response[4]:02X}")
                    return False
            else:
                print(f"⚠️  Réponse trop courte")
                return False
                
        except socket.timeout:
            print(f"❌ Timeout Set Frequency sur {address:02X}h")
            return False
        
    except Exception as e:
        print(f"❌ Erreur Set Frequency {address:02X}h: {e}")
        return False
    finally:
        sock.close()

def main():
    """Test complet des deux adresses CI-V"""
    print("=" * 60)
    print("🔧 TEST ADRESSES CI-V ICOM IC-R8600")
    print("=" * 60)
    
    print(f"📍 Cible: {ICOM_IP}:{ICOM_PORT}")
    print(f"📍 Test des deux adresses configurées:")
    print(f"   → 96h: CI-V Address (USB/série)")
    print(f"   → DFh: USB/LAN Remote Transceive Address (réseau)")
    
    # Test des deux adresses
    addresses = [
        (0x96, "CI-V Address (USB/série)"),
        (0xDF, "USB/LAN Remote Transceive Address (réseau)")
    ]
    
    results = {}
    
    for address, description in addresses:
        # Test ping
        ping_ok = test_address(address, description)
        results[address] = ping_ok
        
        # Si ping OK, test commande
        if ping_ok:
            freq_ok = test_frequency_command(address, description)
            results[f"{address}_freq"] = freq_ok
        
        time.sleep(1)  # Pause entre tests
    
    # Résumé
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    if results.get(0x96):
        print("✅ Adresse 96h (CI-V standard) : FONCTIONNE")
        if results.get("96_freq"):
            print("   → Commandes de fréquence : OK")
    else:
        print("❌ Adresse 96h (CI-V standard) : PAS DE RÉPONSE")
    
    if results.get(0xDF):
        print("✅ Adresse DFh (USB/LAN Remote) : FONCTIONNE")
        if results.get("223_freq"):
            print("   → Commandes de fréquence : OK")
    else:
        print("❌ Adresse DFh (USB/LAN Remote) : PAS DE RÉPONSE")
    
    print("\n🔧 RECOMMANDATIONS:")
    
    if not any(results.get(addr) for addr in [0x96, 0xDF]):
        print("❌ Aucune adresse ne répond")
        print("   → Vérifiez CI-V Enable = ON")
        print("   → Vérifiez CI-V over Network = ON")
        print("   → Redémarrez le récepteur")
    elif results.get(0x96) and not results.get(0xDF):
        print("⚠️  Seule l'adresse 96h répond")
        print("   → Utilisez l'adresse 96h dans le backend")
        print("   → Vérifiez USB/LAN Remote settings")
    elif results.get(0xDF) and not results.get(0x96):
        print("⚠️  Seule l'adresse DFh répond")
        print("   → Utilisez l'adresse DFh dans le backend")
        print("   → Configuration réseau correcte")
    else:
        print("✅ Les deux adresses répondent")
        print("   → Utilisez DFh pour le réseau (recommandé)")
        print("   → Configuration parfaite!")

if __name__ == "__main__":
    main()
