import React, { useState, useEffect, useRef } from 'react';
import { Volume2, VolumeX, Activity, Wifi, WifiOff } from 'lucide-react';

const AudioStreamer = ({ isStreaming, frequency, onStreamToggle }) => {
  const [audioLevel, setAudioLevel] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [audioContext, setAudioContext] = useState(null);
  const [analyser, setAnalyser] = useState(null);
  const [dataArray, setDataArray] = useState(null);
  const [animationId, setAnimationId] = useState(null);
  const canvasRef = useRef(null);
  const audioRef = useRef(null);

  // Initialisation du contexte audio
  useEffect(() => {
    if (isStreaming && !audioContext) {
      const initAudio = async () => {
        try {
          const ctx = new (window.AudioContext || window.webkitAudioContext)();
          const analyserNode = ctx.createAnalyser();
          analyserNode.fftSize = 256;
          
          const bufferLength = analyserNode.frequencyBinCount;
          const dataArr = new Uint8Array(bufferLength);
          
          setAudioContext(ctx);
          setAnalyser(analyserNode);
          setDataArray(dataArr);
          setIsConnected(true);
          
          // Simuler une connexion au stream audio
          startAudioVisualization(analyserNode, dataArr);
          
        } catch (error) {
          console.error('Erreur initialisation audio:', error);
          setIsConnected(false);
        }
      };
      
      initAudio();
    }
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [isStreaming]);

  // Visualisation audio
  const startAudioVisualization = (analyserNode, dataArr) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    const draw = () => {
      // Simuler des données audio (en attendant la vraie connexion)
      const simulatedLevel = Math.random() * 100;
      setAudioLevel(simulatedLevel);
      
      // Effacer le canvas
      ctx.fillStyle = '#1f2937';
      ctx.fillRect(0, 0, width, height);
      
      // Dessiner le spectre
      const barWidth = width / 32;
      let x = 0;
      
      for (let i = 0; i < 32; i++) {
        const barHeight = (Math.random() * height * 0.8) + 10;
        
        // Gradient de couleur basé sur la fréquence
        const hue = (i / 32) * 240; // De rouge à bleu
        ctx.fillStyle = `hsl(${hue}, 70%, 50%)`;
        
        ctx.fillRect(x, height - barHeight, barWidth - 2, barHeight);
        x += barWidth;
      }
      
      // Ligne de niveau audio
      const levelY = height - (simulatedLevel / 100) * height;
      ctx.strokeStyle = '#10b981';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(0, levelY);
      ctx.lineTo(width, levelY);
      ctx.stroke();
      
      const id = requestAnimationFrame(draw);
      setAnimationId(id);
    };
    
    draw();
  };

  // Arrêt du streaming
  const stopStreaming = () => {
    if (animationId) {
      cancelAnimationFrame(animationId);
      setAnimationId(null);
    }
    
    if (audioContext) {
      audioContext.close();
      setAudioContext(null);
    }
    
    setAnalyser(null);
    setDataArray(null);
    setIsConnected(false);
    setAudioLevel(0);
  };

  // Nettoyage à l'arrêt
  useEffect(() => {
    if (!isStreaming) {
      stopStreaming();
    }
  }, [isStreaming]);

  // Formatage de la fréquence
  const formatFrequency = (freq) => {
    return (freq / 1000000).toFixed(3) + ' MHz';
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
      {/* En-tête */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Activity className="text-green-400" size={20} />
          <h3 className="text-lg font-semibold text-white">Audio Live Stream</h3>
        </div>
        
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <Wifi className="text-green-400" size={16} />
          ) : (
            <WifiOff className="text-red-400" size={16} />
          )}
          <span className={`text-sm ${isConnected ? 'text-green-400' : 'text-red-400'}`}>
            {isConnected ? 'Connecté' : 'Déconnecté'}
          </span>
        </div>
      </div>

      {/* Informations de fréquence */}
      <div className="bg-gray-900 rounded p-3 mb-4">
        <div className="flex justify-between items-center">
          <span className="text-gray-400">Fréquence:</span>
          <span className="text-blue-400 font-mono font-bold">
            {formatFrequency(frequency)}
          </span>
        </div>
        <div className="flex justify-between items-center mt-1">
          <span className="text-gray-400">Niveau audio:</span>
          <span className="text-green-400 font-bold">
            {audioLevel.toFixed(0)}%
          </span>
        </div>
      </div>

      {/* Visualiseur audio */}
      <div className="mb-4">
        <canvas
          ref={canvasRef}
          width={400}
          height={120}
          className="w-full h-24 bg-gray-900 rounded border border-gray-600"
        />
      </div>

      {/* Barre de niveau */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-400 mb-1">
          <span>Niveau</span>
          <span>{audioLevel.toFixed(0)}%</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-3">
          <div
            className={`h-3 rounded-full transition-all duration-100 ${
              audioLevel > 80 ? 'bg-red-500' :
              audioLevel > 60 ? 'bg-yellow-500' :
              'bg-green-500'
            }`}
            style={{ width: `${audioLevel}%` }}
          />
        </div>
      </div>

      {/* Contrôles */}
      <div className="flex space-x-2">
        <button
          onClick={onStreamToggle}
          className={`flex-1 flex items-center justify-center px-4 py-2 rounded font-medium transition-colors ${
            isStreaming
              ? 'bg-red-600 hover:bg-red-700 text-white'
              : 'bg-green-600 hover:bg-green-700 text-white'
          }`}
        >
          {isStreaming ? (
            <>
              <VolumeX size={16} className="mr-2" />
              Arrêter Stream
            </>
          ) : (
            <>
              <Volume2 size={16} className="mr-2" />
              Démarrer Stream
            </>
          )}
        </button>
      </div>

      {/* Informations techniques */}
      {isStreaming && (
        <div className="mt-4 text-xs text-gray-400 space-y-1">
          <div>• Format: PCM 48kHz 16-bit</div>
          <div>• Latence: ~50ms</div>
          <div>• Codec: WebRTC</div>
          <div>• Bande passante: ~768 kbps</div>
        </div>
      )}

      {/* Audio element caché pour le streaming réel */}
      <audio
        ref={audioRef}
        style={{ display: 'none' }}
        controls={false}
        autoPlay
        muted={false}
      />
    </div>
  );
};

export default AudioStreamer;
