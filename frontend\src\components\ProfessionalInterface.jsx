import React, { useState, useEffect, useRef } from 'react';
import { 
  Radio, Power, Settings, Activity, Wifi, WifiOff, 
  Monitor, Zap, Signal, Volume2, Clock, Database,
  AlertTriangle, CheckCircle, XCircle, Loader
} from 'lucide-react';
import FrequencyWheel from './FrequencyWheel';
import VirtualLCD from './VirtualLCD';
import AudioControls from './AudioControls';

const ProfessionalInterface = () => {
  // États principaux
  const [radioStatus, setRadioStatus] = useState({
    power_on: false,
    frequency: 145000000,
    mode: 'FM',
    rf_gain: 50,
    rssi: -80,
    volume: 50,
    squelch: 0,
    filter_width: 15000,
    scanning: false,
    recording: false
  });

  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [systemStats, setSystemStats] = useState({
    latency: 0,
    commands_sent: 0,
    errors: 0,
    uptime: 0
  });

  const [notifications, setNotifications] = useState([]);
  const [activeTab, setActiveTab] = useState('control');
  const [isLoading, setIsLoading] = useState(false);

  // WebSocket pour temps réel
  const wsRef = useRef(null);

  // Connexion WebSocket
  useEffect(() => {
    const connectWebSocket = () => {
      try {
        wsRef.current = new WebSocket('ws://localhost:8000/ws');
        
        wsRef.current.onopen = () => {
          setConnectionStatus('connected');
          addNotification('Connexion WebSocket établie', 'success');
        };
        
        wsRef.current.onmessage = (event) => {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        };
        
        wsRef.current.onclose = () => {
          setConnectionStatus('disconnected');
          addNotification('Connexion WebSocket fermée', 'warning');
          // Reconnexion automatique
          setTimeout(connectWebSocket, 3000);
        };
        
        wsRef.current.onerror = (error) => {
          setConnectionStatus('error');
          addNotification('Erreur WebSocket', 'error');
        };
        
      } catch (error) {
        console.error('Erreur connexion WebSocket:', error);
      }
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  // Gestion des messages WebSocket
  const handleWebSocketMessage = (message) => {
    switch (message.type) {
      case 'status_update':
        setRadioStatus(prev => ({ ...prev, ...message.data }));
        break;
      case 'frequency_change':
        setRadioStatus(prev => ({ 
          ...prev, 
          frequency: message.data.frequency,
          mode: message.data.mode 
        }));
        break;
      case 'signal_update':
        setRadioStatus(prev => ({ ...prev, rssi: message.data.rssi }));
        break;
      case 'connection_status':
        setConnectionStatus(message.data.status);
        break;
      case 'error':
        addNotification(message.data.message, 'error');
        break;
      case 'notification':
        addNotification(message.data.message, 'info');
        break;
      default:
        console.log('Message WebSocket non géré:', message);
    }
  };

  // Gestion des notifications
  const addNotification = (message, type = 'info') => {
    const notification = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date().toLocaleTimeString()
    };
    
    setNotifications(prev => [notification, ...prev.slice(0, 4)]);
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 5000);
  };

  // Envoi de commandes via WebSocket
  const sendCommand = (command, params = {}) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const message = {
        type: 'command',
        data: { command, params },
        timestamp: Date.now()
      };
      
      wsRef.current.send(JSON.stringify(message));
      setSystemStats(prev => ({ ...prev, commands_sent: prev.commands_sent + 1 }));
    } else {
      addNotification('Pas de connexion WebSocket', 'error');
    }
  };

  // Gestionnaires d'événements
  const handleFrequencyChange = (frequency) => {
    setRadioStatus(prev => ({ ...prev, frequency }));
    sendCommand('set_frequency', { frequency });
  };

  const handleModeChange = (mode) => {
    setRadioStatus(prev => ({ ...prev, mode }));
    sendCommand('set_mode', { mode });
  };

  const handlePowerToggle = () => {
    const newPowerState = !radioStatus.power_on;
    setRadioStatus(prev => ({ ...prev, power_on: newPowerState }));
    sendCommand('power_control', { power_on: newPowerState });
  };

  const handleVolumeChange = (volume) => {
    setRadioStatus(prev => ({ ...prev, volume }));
    sendCommand('set_volume', { volume });
  };

  const handleSquelchChange = (squelch) => {
    setRadioStatus(prev => ({ ...prev, squelch }));
    sendCommand('set_squelch', { squelch });
  };

  const handleAudioCommand = (command, params) => {
    sendCommand(`audio_${command}`, params);
  };

  // Indicateur de statut de connexion
  const ConnectionIndicator = () => (
    <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${
      connectionStatus === 'connected' 
        ? 'bg-green-900 text-green-300' 
        : connectionStatus === 'error'
        ? 'bg-red-900 text-red-300'
        : 'bg-yellow-900 text-yellow-300'
    }`}>
      {connectionStatus === 'connected' ? (
        <><Wifi size={16} /> Connecté</>
      ) : connectionStatus === 'error' ? (
        <><WifiOff size={16} /> Erreur</>
      ) : (
        <><Loader size={16} className="animate-spin" /> Connexion...</>
      )}
    </div>
  );

  // Panneau de notifications
  const NotificationPanel = () => (
    <div className="space-y-2">
      {notifications.map(notification => (
        <div
          key={notification.id}
          className={`p-3 rounded-lg border-l-4 ${
            notification.type === 'success' 
              ? 'bg-green-900 border-green-500 text-green-300'
              : notification.type === 'error'
              ? 'bg-red-900 border-red-500 text-red-300'
              : notification.type === 'warning'
              ? 'bg-yellow-900 border-yellow-500 text-yellow-300'
              : 'bg-blue-900 border-blue-500 text-blue-300'
          }`}
        >
          <div className="flex items-center justify-between">
            <span className="text-sm">{notification.message}</span>
            <span className="text-xs opacity-75">{notification.timestamp}</span>
          </div>
        </div>
      ))}
    </div>
  );

  // Onglets de navigation
  const TabButton = ({ id, label, icon: Icon, active, onClick }) => (
    <button
      onClick={() => onClick(id)}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
        active 
          ? 'bg-blue-600 text-white' 
          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
      }`}
    >
      <Icon size={18} />
      <span>{label}</span>
    </button>
  );

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* En-tête */}
      <header className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Radio size={32} className="text-blue-500" />
              <div>
                <h1 className="text-2xl font-bold">ICOM IC-R8600</h1>
                <p className="text-sm text-gray-400">Professional Radio Controller</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <ConnectionIndicator />
            <div className="text-right text-sm">
              <div className="text-gray-300">
                {new Date().toLocaleTimeString()}
              </div>
              <div className="text-gray-500">
                Latence: {systemStats.latency}ms
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation par onglets */}
      <nav className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex space-x-2">
          <TabButton 
            id="control" 
            label="Contrôle" 
            icon={Settings} 
            active={activeTab === 'control'}
            onClick={setActiveTab}
          />
          <TabButton 
            id="audio" 
            label="Audio" 
            icon={Volume2} 
            active={activeTab === 'audio'}
            onClick={setActiveTab}
          />
          <TabButton 
            id="monitor" 
            label="Monitoring" 
            icon={Activity} 
            active={activeTab === 'monitor'}
            onClick={setActiveTab}
          />
          <TabButton 
            id="database" 
            label="Base de données" 
            icon={Database} 
            active={activeTab === 'database'}
            onClick={setActiveTab}
          />
        </div>
      </nav>

      {/* Contenu principal */}
      <main className="p-6">
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          
          {/* Colonne principale */}
          <div className="xl:col-span-3 space-y-6">
            
            {/* Affichage LCD virtuel */}
            <VirtualLCD 
              radioStatus={radioStatus}
              connectionStatus={connectionStatus}
              className="w-full"
            />

            {/* Contenu selon l'onglet actif */}
            {activeTab === 'control' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Molette de fréquence */}
                <div className="bg-gray-800 rounded-lg p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Zap size={20} className="mr-2 text-yellow-500" />
                    Contrôle de Fréquence
                  </h3>
                  <FrequencyWheel
                    frequency={radioStatus.frequency}
                    onFrequencyChange={handleFrequencyChange}
                    disabled={!radioStatus.power_on}
                    step={25000}
                  />
                </div>

                {/* Contrôles de mode et puissance */}
                <div className="bg-gray-800 rounded-lg p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Settings size={20} className="mr-2 text-blue-500" />
                    Contrôles Principaux
                  </h3>
                  
                  {/* Bouton d'alimentation */}
                  <button
                    onClick={handlePowerToggle}
                    className={`w-full mb-4 py-3 px-6 rounded-lg font-bold text-lg transition-colors ${
                      radioStatus.power_on
                        ? 'bg-red-600 hover:bg-red-700 text-white'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    }`}
                  >
                    <Power size={24} className="inline mr-2" />
                    {radioStatus.power_on ? 'ÉTEINDRE' : 'ALLUMER'}
                  </button>

                  {/* Sélection du mode */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-2">Mode de modulation</label>
                    <select
                      value={radioStatus.mode}
                      onChange={(e) => handleModeChange(e.target.value)}
                      disabled={!radioStatus.power_on}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="LSB">LSB</option>
                      <option value="USB">USB</option>
                      <option value="AM">AM</option>
                      <option value="CW">CW</option>
                      <option value="FM">FM</option>
                      <option value="WFM">WFM</option>
                      <option value="RTTY">RTTY</option>
                    </select>
                  </div>

                  {/* RF Gain */}
                  <div>
                    <label className="block text-sm font-medium mb-2">RF Gain</label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={radioStatus.rf_gain}
                      onChange={(e) => setRadioStatus(prev => ({ ...prev, rf_gain: parseInt(e.target.value) }))}
                      disabled={!radioStatus.power_on}
                      className="w-full"
                    />
                    <div className="text-center text-sm text-gray-400 mt-1">
                      {radioStatus.rf_gain}%
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'audio' && (
              <AudioControls
                radioStatus={radioStatus}
                onVolumeChange={handleVolumeChange}
                onSquelchChange={handleSquelchChange}
                onAudioCommand={handleAudioCommand}
                isConnected={connectionStatus === 'connected'}
                className="bg-gray-800"
              />
            )}

            {activeTab === 'monitor' && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Monitoring en temps réel</h3>
                <p className="text-gray-400">Fonctionnalité de monitoring à implémenter...</p>
              </div>
            )}

            {activeTab === 'database' && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Gestion de la base de données</h3>
                <p className="text-gray-400">Interface de gestion des fréquences favorites à implémenter...</p>
              </div>
            )}
          </div>

          {/* Panneau latéral */}
          <div className="space-y-6">
            {/* Statistiques système */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h4 className="font-semibold mb-3 flex items-center">
                <Monitor size={18} className="mr-2 text-green-500" />
                Statistiques
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Commandes:</span>
                  <span className="font-mono">{systemStats.commands_sent}</span>
                </div>
                <div className="flex justify-between">
                  <span>Erreurs:</span>
                  <span className="font-mono text-red-400">{systemStats.errors}</span>
                </div>
                <div className="flex justify-between">
                  <span>Latence:</span>
                  <span className="font-mono">{systemStats.latency}ms</span>
                </div>
              </div>
            </div>

            {/* Notifications */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h4 className="font-semibold mb-3 flex items-center">
                <AlertTriangle size={18} className="mr-2 text-yellow-500" />
                Notifications
              </h4>
              <NotificationPanel />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ProfessionalInterface;
