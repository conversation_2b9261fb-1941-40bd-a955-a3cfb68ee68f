import React, { useState, useEffect } from 'react';
import { Signal, Volume2, Zap, Radio, Wifi, WifiOff, Battery, Clock } from 'lucide-react';

const VirtualLCD = ({ 
  radioStatus = {}, 
  connectionStatus = 'disconnected',
  className = "" 
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [blinkState, setBlinkState] = useState(true);

  // Mise à jour de l'heure
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Animation de clignotement pour les indicateurs
  useEffect(() => {
    const blinkTimer = setInterval(() => {
      setBlinkState(prev => !prev);
    }, 500);
    return () => clearInterval(blinkTimer);
  }, []);

  // Formatage de la fréquence pour l'affichage LCD
  const formatLCDFrequency = (freq) => {
    if (!freq) return "000.000.000";
    
    const freqStr = freq.toString().padStart(9, '0');
    return `${freqStr.slice(0, 3)}.${freqStr.slice(3, 6)}.${freqStr.slice(6, 9)}`;
  };

  // Formatage du mode pour l'affichage
  const formatMode = (mode) => {
    return (mode || 'FM').padEnd(4, ' ');
  };

  // Calcul du niveau de signal (S-meter)
  const getSignalBars = (rssi) => {
    if (!rssi || rssi < -120) return 0;
    if (rssi > -60) return 9;
    return Math.max(0, Math.floor((rssi + 120) / 7));
  };

  // Indicateurs d'état
  const isConnected = connectionStatus === 'connected';
  const isPowered = radioStatus.power_on;
  const isScanning = radioStatus.scanning;
  const isRecording = radioStatus.recording;

  return (
    <div className={`virtual-lcd ${className}`}>
      {/* Cadre externe du LCD */}
      <div className="bg-gray-900 p-6 rounded-lg shadow-2xl border-4 border-gray-700">
        
        {/* Écran LCD principal */}
        <div className="bg-black p-4 rounded border-2 border-gray-600 relative overflow-hidden">
          
          {/* Effet de rétroéclairage */}
          <div className={`absolute inset-0 ${isPowered ? 'bg-green-900' : 'bg-gray-800'} opacity-20`} />
          
          {/* Contenu de l'écran */}
          <div className={`relative z-10 font-mono ${isPowered ? 'text-green-400' : 'text-gray-600'}`}>
            
            {/* Ligne 1: Fréquence principale */}
            <div className="text-4xl font-bold text-center mb-2 tracking-wider">
              {isPowered ? formatLCDFrequency(radioStatus.frequency) : "----.---.---"}
              <span className="text-lg ml-2">MHz</span>
            </div>
            
            {/* Ligne 2: Mode et informations */}
            <div className="flex justify-between items-center mb-3 text-lg">
              <div className="flex items-center space-x-4">
                <span className="bg-green-800 px-2 py-1 rounded text-green-200">
                  {isPowered ? formatMode(radioStatus.mode) : "----"}
                </span>
                
                {/* Indicateur de scan */}
                {isScanning && (
                  <span className={`${blinkState ? 'text-yellow-400' : 'text-yellow-600'} font-bold`}>
                    SCAN
                  </span>
                )}
                
                {/* Indicateur d'enregistrement */}
                {isRecording && (
                  <span className={`${blinkState ? 'text-red-400' : 'text-red-600'} font-bold`}>
                    ● REC
                  </span>
                )}
              </div>
              
              {/* Heure */}
              <div className="text-sm">
                {currentTime.toLocaleTimeString('fr-FR', { 
                  hour: '2-digit', 
                  minute: '2-digit',
                  second: '2-digit'
                })}
              </div>
            </div>
            
            {/* Ligne 3: S-meter et niveaux */}
            <div className="mb-3">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm">S-METER</span>
                <span className="text-sm">
                  {isPowered ? `${radioStatus.rssi || -80} dBm` : "--- dBm"}
                </span>
              </div>
              
              {/* Barre de signal */}
              <div className="flex space-x-1 mb-2">
                {Array.from({ length: 9 }, (_, i) => (
                  <div
                    key={i}
                    className={`h-3 w-6 border border-green-600 ${
                      isPowered && i < getSignalBars(radioStatus.rssi)
                        ? 'bg-green-400'
                        : 'bg-gray-700'
                    }`}
                  />
                ))}
                <span className="text-xs ml-2">S9</span>
              </div>
            </div>
            
            {/* Ligne 4: Contrôles et niveaux */}
            <div className="grid grid-cols-3 gap-4 text-sm">
              {/* Volume */}
              <div>
                <div className="flex items-center mb-1">
                  <Volume2 size={12} className="mr-1" />
                  <span>VOL</span>
                </div>
                <div className="bg-gray-700 h-2 rounded">
                  <div 
                    className="bg-blue-400 h-2 rounded transition-all duration-300"
                    style={{ width: `${isPowered ? (radioStatus.volume || 50) : 0}%` }}
                  />
                </div>
                <div className="text-xs text-center mt-1">
                  {isPowered ? (radioStatus.volume || 50) : 0}
                </div>
              </div>
              
              {/* RF Gain */}
              <div>
                <div className="flex items-center mb-1">
                  <Zap size={12} className="mr-1" />
                  <span>RF</span>
                </div>
                <div className="bg-gray-700 h-2 rounded">
                  <div 
                    className="bg-yellow-400 h-2 rounded transition-all duration-300"
                    style={{ width: `${isPowered ? ((radioStatus.rf_gain || 128) / 255 * 100) : 0}%` }}
                  />
                </div>
                <div className="text-xs text-center mt-1">
                  {isPowered ? (radioStatus.rf_gain || 128) : 0}
                </div>
              </div>
              
              {/* Squelch */}
              <div>
                <div className="flex items-center mb-1">
                  <Signal size={12} className="mr-1" />
                  <span>SQL</span>
                </div>
                <div className="bg-gray-700 h-2 rounded">
                  <div 
                    className="bg-purple-400 h-2 rounded transition-all duration-300"
                    style={{ width: `${isPowered ? (radioStatus.squelch || 0) : 0}%` }}
                  />
                </div>
                <div className="text-xs text-center mt-1">
                  {isPowered ? (radioStatus.squelch || 0) : 0}
                </div>
              </div>
            </div>
            
            {/* Ligne 5: Indicateurs d'état */}
            <div className="flex justify-between items-center mt-3 pt-2 border-t border-gray-600">
              {/* Connexion */}
              <div className="flex items-center space-x-2">
                {isConnected ? (
                  <Wifi size={16} className="text-green-400" />
                ) : (
                  <WifiOff size={16} className="text-red-400" />
                )}
                <span className="text-xs">
                  {isConnected ? 'ONLINE' : 'OFFLINE'}
                </span>
              </div>
              
              {/* Alimentation */}
              <div className="flex items-center space-x-2">
                <Battery size={16} className={isPowered ? 'text-green-400' : 'text-gray-600'} />
                <span className="text-xs">
                  {isPowered ? 'PWR' : 'OFF'}
                </span>
              </div>
              
              {/* Filtre */}
              <div className="text-xs">
                FILTER: {isPowered ? (radioStatus.filter_width || 15) : 0} kHz
              </div>
            </div>
          </div>
          
          {/* Effet de scan lines pour réalisme */}
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: 20 }, (_, i) => (
              <div
                key={i}
                className="absolute w-full h-px bg-green-400 opacity-5"
                style={{ top: `${i * 5}%` }}
              />
            ))}
          </div>
        </div>
        
        {/* Boutons de contrôle sous l'écran */}
        <div className="flex justify-center space-x-4 mt-4">
          <div className="w-8 h-8 bg-gray-600 rounded-full border-2 border-gray-500 flex items-center justify-center">
            <div className="w-2 h-2 bg-red-500 rounded-full" />
          </div>
          <div className="w-8 h-8 bg-gray-600 rounded-full border-2 border-gray-500 flex items-center justify-center">
            <div className="w-2 h-2 bg-green-500 rounded-full" />
          </div>
          <div className="w-8 h-8 bg-gray-600 rounded-full border-2 border-gray-500 flex items-center justify-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full" />
          </div>
        </div>
        
        {/* Étiquette du modèle */}
        <div className="text-center mt-2">
          <div className="text-gray-400 text-sm font-bold">ICOM IC-R8600</div>
          <div className="text-gray-500 text-xs">Communications Receiver</div>
        </div>
      </div>
    </div>
  );
};

export default VirtualLCD;
