"""
Serveur FastAPI ultra-simplifié pour ICOM IC-R8600
Mode simulation pure - Pas d'erreurs, toujours fonctionnel
"""

import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Modèles de données
class CommandRequest(BaseModel):
    frequency: Optional[int] = None
    mode: Optional[str] = None
    volume: Optional[int] = None
    squelch: Optional[int] = None
    rf_gain: Optional[int] = None

class AudioRequest(BaseModel):
    audio_type: str = "AF"
    frequency: Optional[int] = None
    record: bool = False

class StatusResponse(BaseModel):
    frequency: int
    mode: str
    volume: int
    squelch: int
    power_on: bool
    signal_level: int
    connection_status: str

# Application FastAPI
app = FastAPI(
    title="ICOM IC-R8600 Ultra Simple Controller",
    description="Interface ultra-simplifiée pour contrôle ICOM IC-R8600",
    version="1.0.0"
)

# CORS pour frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# État global du récepteur
radio_state = {
    "frequency": 145500000,
    "mode": "FM",
    "volume": 50,
    "squelch": 10,
    "power_on": False,
    "signal_level": 0,
    "connection_status": "disconnected",
    "last_update": None
}

# Configuration réseau
network_config = {
    "host": "*************",
    "port": 50001,
    "protocol": "UDP",
    "timeout": 3.0,
    "simulation_mode": True
}

def load_config():
    """Charger la configuration depuis config.json"""
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
            if 'icom' in config:
                icom_config = config['icom']
                network_config.update({
                    "host": icom_config.get("udp_host", "*************"),
                    "port": icom_config.get("udp_port", 50001),
                    "timeout": icom_config.get("network_timeout", 3.0)
                })
        logger.info(f"Configuration chargée: {network_config}")
    except Exception as e:
        logger.warning(f"Impossible de charger config.json: {e}")

def simulate_command(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Simulation ultra-simple des commandes ICOM
    Toujours réussit, pas d'erreurs
    """
    try:
        results = []
        
        # Traiter chaque commande
        if "frequency" in command:
            freq = command["frequency"]
            if 100000 <= freq <= 3000000000:  # Plage valide
                radio_state["frequency"] = freq
                results.append(f"Fréquence {freq} Hz: OK")
            else:
                results.append(f"Fréquence {freq} Hz: ERREUR - Hors plage")
        
        if "mode" in command:
            mode = command["mode"]
            valid_modes = ["FM", "AM", "USB", "LSB", "WFM", "CW", "DV"]
            if mode in valid_modes:
                radio_state["mode"] = mode
                results.append(f"Mode {mode}: OK")
            else:
                results.append(f"Mode {mode}: ERREUR - Mode invalide")
        
        if "volume" in command:
            vol = command["volume"]
            if 0 <= vol <= 100:
                radio_state["volume"] = vol
                results.append(f"Volume {vol}%: OK")
            else:
                results.append(f"Volume {vol}%: ERREUR - Hors plage")
        
        if "squelch" in command:
            sq = command["squelch"]
            if 0 <= sq <= 100:
                radio_state["squelch"] = sq
                results.append(f"Squelch {sq}%: OK")
            else:
                results.append(f"Squelch {sq}%: ERREUR - Hors plage")
        
        # Mettre à jour l'état
        radio_state["last_update"] = datetime.now().isoformat()
        radio_state["connection_status"] = "connected"
        
        return {
            "success": True,
            "message": "; ".join(results) if results else "Aucune commande",
            "data": radio_state.copy()
        }
        
    except Exception as e:
        logger.error(f"Erreur simulation commande: {e}")
        return {
            "success": False,
            "message": f"Erreur: {str(e)}",
            "data": radio_state.copy()
        }

@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage"""
    logger.info("Démarrage du serveur ICOM IC-R8600 Ultra Simple")
    load_config()
    
    # Créer le dossier recordings s'il n'existe pas
    os.makedirs("recordings", exist_ok=True)
    
    logger.info("Mode simulation pure - Toujours fonctionnel")

@app.get("/")
async def root():
    """Page d'accueil de l'API"""
    return {
        "message": "ICOM IC-R8600 Ultra Simple Controller API",
        "version": "1.0.0",
        "status": "running",
        "mode": "simulation",
        "radio_state": radio_state
    }

@app.post("/api/command")
async def send_command(command: CommandRequest):
    """Envoyer une commande au récepteur"""
    try:
        logger.info(f"Commande reçue: {command.model_dump(exclude_none=True)}")
        
        # Convertir en dictionnaire sans valeurs None
        cmd_dict = command.model_dump(exclude_none=True)
        
        if not cmd_dict:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "message": "Aucune commande fournie",
                    "data": radio_state.copy()
                }
            )
        
        # Simuler la commande
        result = simulate_command(cmd_dict)
        
        logger.info(f"Résultat commande: {result['message']}")
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"Erreur traitement commande: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": f"Erreur serveur: {str(e)}",
                "data": radio_state.copy()
            }
        )

@app.get("/api/status")
async def get_status():
    """Obtenir l'état actuel du récepteur"""
    try:
        return StatusResponse(
            frequency=radio_state["frequency"],
            mode=radio_state["mode"],
            volume=radio_state["volume"],
            squelch=radio_state["squelch"],
            power_on=radio_state["power_on"],
            signal_level=radio_state["signal_level"],
            connection_status=radio_state["connection_status"]
        )
    except Exception as e:
        logger.error(f"Erreur lecture statut: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/power")
async def toggle_power(power_on: bool):
    """Allumer/éteindre le récepteur"""
    try:
        radio_state["power_on"] = power_on
        radio_state["last_update"] = datetime.now().isoformat()
        
        if power_on:
            radio_state["connection_status"] = "connected"
            logger.info("Récepteur allumé (simulation)")
        else:
            radio_state["connection_status"] = "disconnected"
            radio_state["signal_level"] = 0
            logger.info("Récepteur éteint (simulation)")
        
        return {
            "success": True,
            "message": f"Récepteur {'allumé' if power_on else 'éteint'} (simulation)",
            "power_on": power_on
        }
        
    except Exception as e:
        logger.error(f"Erreur changement alimentation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/audio/start")
async def start_audio(request: AudioRequest):
    """Démarrer l'écoute ou l'enregistrement audio"""
    try:
        logger.info(f"Démarrage audio: {request.model_dump()}")
        
        if not radio_state["power_on"]:
            return JSONResponse(
                content={
                    "success": False,
                    "message": "Récepteur éteint",
                    "data": radio_state.copy()
                }
            )
        
        # Mode simulation audio
        action = "enregistrement" if request.record else "écoute"
        logger.info(f"Mode simulation: {action}")
        
        return {
            "success": True,
            "message": f"Démarrage {action} sur {radio_state['frequency']} Hz (simulation)",
            "frequency": radio_state["frequency"],
            "mode": radio_state["mode"],
            "recording": request.record
        }
        
    except Exception as e:
        logger.error(f"Erreur démarrage audio: {e}")
        return JSONResponse(
            content={
                "success": False,
                "message": f"Erreur audio: {str(e)}",
                "data": radio_state.copy()
            }
        )

@app.post("/api/audio/stop")
async def stop_audio():
    """Arrêter l'écoute ou l'enregistrement audio"""
    try:
        logger.info("Arrêt audio (simulation)")
        
        return {
            "success": True,
            "message": "Audio arrêté (simulation)",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Erreur arrêt audio: {e}")
        return JSONResponse(
            content={
                "success": False,
                "message": f"Erreur arrêt audio: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

@app.get("/api/recordings")
async def list_recordings():
    """Lister les enregistrements disponibles"""
    try:
        recordings_dir = "recordings"
        recordings = []
        
        if os.path.exists(recordings_dir):
            for filename in os.listdir(recordings_dir):
                if filename.endswith(('.wav', '.mp3')):
                    filepath = os.path.join(recordings_dir, filename)
                    stat = os.stat(filepath)
                    recordings.append({
                        "filename": filename,
                        "size": stat.st_size,
                        "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "duration": 0  # À calculer si nécessaire
                    })
        
        return recordings
        
    except Exception as e:
        logger.error(f"Erreur liste enregistrements: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Vérification de santé du service"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "mode": "simulation",
        "radio_connected": radio_state["connection_status"] == "connected",
        "power_on": radio_state["power_on"]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
