import React from 'react';
import { 
  Zap, RotateCw, RotateCcw, Volume2, VolumeX, 
  Pause, Play, Square, Mic, Settings, Lock, Unlock 
} from 'lucide-react';

const QuickControls = ({ 
  frequency, 
  setFrequency, 
  volume, 
  setVolume, 
  isStreaming, 
  toggleStreaming, 
  isRecording, 
  toggleRecording,
  powerOn,
  onPowerToggle 
}) => {
  
  // Pas de fréquence rapides
  const frequencySteps = [
    { label: '1 Hz', value: 1 },
    { label: '10 Hz', value: 10 },
    { label: '100 Hz', value: 100 },
    { label: '1 kHz', value: 1000 },
    { label: '10 kHz', value: 10000 },
    { label: '25 kHz', value: 25000 },
    { label: '100 kHz', value: 100000 },
    { label: '1 MHz', value: 1000000 }
  ];

  // Bandes de fréquences courantes
  const frequencyBands = [
    { name: 'VLF', start: 3000, end: 30000, color: 'bg-purple-600' },
    { name: 'L<PERSON>', start: 30000, end: 300000, color: 'bg-indigo-600' },
    { name: 'MF', start: 300000, end: 3000000, color: 'bg-blue-600' },
    { name: 'HF', start: 3000000, end: 30000000, color: 'bg-green-600' },
    { name: 'VHF', start: 30000000, end: 300000000, color: 'bg-yellow-600' },
    { name: 'UHF', start: 300000000, end: 3000000000, color: 'bg-orange-600' }
  ];

  // Fréquences d'urgence
  const emergencyFreqs = [
    { name: 'Marine 16', freq: 156800000, desc: 'Canal d\'urgence maritime' },
    { name: 'Aviation', freq: 121500000, desc: 'Urgence aviation' },
    { name: 'Police', freq: 162000000, desc: 'Fréquence police' },
    { name: 'Pompiers', freq: 161000000, desc: 'Fréquence pompiers' }
  ];

  const adjustFrequency = (step) => {
    if (!powerOn) return;
    const newFreq = Math.max(100000, Math.min(3000000000, frequency + step));
    setFrequency(newFreq);
  };

  const jumpToBand = (band) => {
    if (!powerOn) return;
    const centerFreq = (band.start + band.end) / 2;
    setFrequency(Math.round(centerFreq));
  };

  const setEmergencyFreq = (emergencyFreq) => {
    if (!powerOn) return;
    setFrequency(emergencyFreq.freq);
  };

  const getCurrentBand = () => {
    return frequencyBands.find(band => 
      frequency >= band.start && frequency <= band.end
    );
  };

  const formatFrequency = (freq) => {
    if (freq >= 1000000) {
      return `${(freq / 1000000).toFixed(3)} MHz`;
    } else if (freq >= 1000) {
      return `${(freq / 1000).toFixed(1)} kHz`;
    }
    return `${freq} Hz`;
  };

  const currentBand = getCurrentBand();

  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-600 space-y-4">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <Zap className="mr-2 text-yellow-400" size={20} />
          Contrôles Rapides
        </h3>
        
        {currentBand && (
          <span className={`px-3 py-1 rounded text-white text-sm font-medium ${currentBand.color}`}>
            {currentBand.name}
          </span>
        )}
      </div>

      {/* Contrôles de fréquence */}
      <div>
        <h4 className="text-sm font-medium text-gray-300 mb-2">Ajustement Fréquence</h4>
        <div className="grid grid-cols-4 gap-2">
          {frequencySteps.map(step => (
            <div key={step.value} className="flex">
              <button
                onClick={() => adjustFrequency(-step.value)}
                disabled={!powerOn}
                className="flex-1 px-2 py-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 rounded-l text-xs transition-colors"
                title={`-${step.label}`}
              >
                <RotateCcw size={12} />
              </button>
              <div className="px-2 py-1 bg-gray-700 text-xs text-center min-w-0 flex-1 text-white">
                {step.label}
              </div>
              <button
                onClick={() => adjustFrequency(step.value)}
                disabled={!powerOn}
                className="flex-1 px-2 py-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded-r text-xs transition-colors"
                title={`+${step.label}`}
              >
                <RotateCw size={12} />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Bandes de fréquences */}
      <div>
        <h4 className="text-sm font-medium text-gray-300 mb-2">Bandes de Fréquences</h4>
        <div className="grid grid-cols-3 gap-2">
          {frequencyBands.map(band => (
            <button
              key={band.name}
              onClick={() => jumpToBand(band)}
              disabled={!powerOn}
              className={`px-3 py-2 rounded text-white text-sm font-medium transition-colors ${
                currentBand?.name === band.name 
                  ? `${band.color} ring-2 ring-white` 
                  : `${band.color} hover:opacity-80 disabled:bg-gray-600`
              }`}
            >
              {band.name}
              <div className="text-xs opacity-75">
                {formatFrequency(band.start)} - {formatFrequency(band.end)}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Fréquences d'urgence */}
      <div>
        <h4 className="text-sm font-medium text-gray-300 mb-2">Fréquences d'Urgence</h4>
        <div className="grid grid-cols-2 gap-2">
          {emergencyFreqs.map(emergency => (
            <button
              key={emergency.name}
              onClick={() => setEmergencyFreq(emergency)}
              disabled={!powerOn}
              className="px-3 py-2 bg-red-700 hover:bg-red-800 disabled:bg-gray-600 rounded text-white text-sm transition-colors"
              title={emergency.desc}
            >
              <div className="font-medium">{emergency.name}</div>
              <div className="text-xs opacity-75">
                {formatFrequency(emergency.freq)}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Contrôles audio et enregistrement */}
      <div>
        <h4 className="text-sm font-medium text-gray-300 mb-2">Audio & Enregistrement</h4>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={toggleStreaming}
            disabled={!powerOn}
            className={`flex items-center justify-center px-3 py-2 rounded font-medium transition-colors ${
              isStreaming
                ? 'bg-yellow-600 hover:bg-yellow-700'
                : 'bg-green-600 hover:bg-green-700'
            } disabled:bg-gray-600 text-white`}
          >
            {isStreaming ? <Pause size={16} className="mr-1" /> : <Play size={16} className="mr-1" />}
            {isStreaming ? 'Stop' : 'Live'}
          </button>

          <button
            onClick={toggleRecording}
            disabled={!powerOn}
            className={`flex items-center justify-center px-3 py-2 rounded font-medium transition-colors ${
              isRecording
                ? 'bg-red-600 hover:bg-red-700'
                : 'bg-blue-600 hover:bg-blue-700'
            } disabled:bg-gray-600 text-white`}
          >
            {isRecording ? <Square size={16} className="mr-1" /> : <Mic size={16} className="mr-1" />}
            {isRecording ? 'Stop' : 'Rec'}
          </button>
        </div>

        {/* Contrôle de volume rapide */}
        <div className="mt-3">
          <div className="flex items-center space-x-2">
            <VolumeX size={16} className="text-gray-400" />
            <input
              type="range"
              min="0"
              max="100"
              value={volume}
              onChange={(e) => setVolume(parseInt(e.target.value))}
              disabled={!powerOn}
              className="flex-1"
            />
            <Volume2 size={16} className="text-gray-400" />
            <span className="text-white text-sm w-12 text-right">{volume}%</span>
          </div>
        </div>
      </div>

      {/* Bouton d'alimentation */}
      <div className="pt-2 border-t border-gray-600">
        <button
          onClick={onPowerToggle}
          className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-bold transition-all ${
            powerOn
              ? 'bg-red-600 hover:bg-red-700 text-white'
              : 'bg-green-600 hover:bg-green-700 text-white'
          }`}
        >
          {powerOn ? <Lock size={20} className="mr-2" /> : <Unlock size={20} className="mr-2" />}
          {powerOn ? 'POWER OFF' : 'POWER ON'}
        </button>
      </div>
    </div>
  );
};

export default QuickControls;
