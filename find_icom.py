#!/usr/bin/env python3
"""
Script pour trouver l'adresse IP du récepteur ICOM IC-R8600 sur le réseau
"""

import socket
import subprocess
import re
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed

def ping_host(ip):
    """Ping une adresse IP"""
    try:
        result = subprocess.run(
            ['ping', '-n', '1', '-w', '1000', ip], 
            capture_output=True, 
            text=True,
            timeout=2
        )
        return ip if result.returncode == 0 else None
    except:
        return None

def test_icom_port(ip, port=50001):
    """Tester si le port CI-V est ouvert"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except:
        return False

def get_network_info():
    """Obtenir les informations réseau du PC"""
    try:
        result = subprocess.run(['ipconfig'], capture_output=True, text=True)
        output = result.stdout
        
        # Extraire l'adresse IP et le masque
        ip_pattern = r'Adresse IPv4.*?:\s*(\d+\.\d+\.\d+\.\d+)'
        mask_pattern = r'Masque de sous-réseau.*?:\s*(\d+\.\d+\.\d+\.\d+)'
        
        ip_match = re.search(ip_pattern, output)
        mask_match = re.search(mask_pattern, output)
        
        if ip_match:
            pc_ip = ip_match.group(1)
            print(f"🖥️  IP de votre PC: {pc_ip}")
            
            # Calculer le réseau
            ip_parts = pc_ip.split('.')
            network = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}"
            return network
        
    except Exception as e:
        print(f"Erreur lecture config réseau: {e}")
    
    return "192.168.1"

def scan_network(network_base):
    """Scanner le réseau pour trouver des appareils actifs"""
    print(f"🔍 Scan du réseau {network_base}.0/24...")
    
    active_ips = []
    
    # Créer une liste d'IPs à tester
    ips_to_test = [f"{network_base}.{i}" for i in range(1, 255)]
    
    # Test en parallèle
    with ThreadPoolExecutor(max_workers=50) as executor:
        future_to_ip = {executor.submit(ping_host, ip): ip for ip in ips_to_test}
        
        for future in as_completed(future_to_ip):
            result = future.result()
            if result:
                active_ips.append(result)
                print(f"✅ Appareil trouvé: {result}")
    
    return active_ips

def test_icom_devices(active_ips):
    """Tester quels appareils ont le port CI-V ouvert"""
    print(f"\n🎛️  Test du port CI-V (50001) sur les appareils trouvés...")
    
    icom_candidates = []
    
    for ip in active_ips:
        if test_icom_port(ip, 50001):
            icom_candidates.append(ip)
            print(f"🎯 ICOM possible trouvé: {ip}:50001")
        else:
            print(f"❌ {ip} - Port 50001 fermé")
    
    return icom_candidates

def main():
    print("🔌 Recherche du récepteur ICOM IC-R8600 sur le réseau")
    print("=" * 50)
    
    # 1. Obtenir les infos réseau du PC
    network_base = get_network_info()
    print(f"📡 Réseau détecté: {network_base}.0/24")
    
    # 2. Scanner le réseau
    active_ips = scan_network(network_base)
    
    if not active_ips:
        print("❌ Aucun appareil trouvé sur le réseau")
        return
    
    print(f"\n📋 {len(active_ips)} appareils actifs trouvés")
    
    # 3. Tester les ports CI-V
    icom_candidates = test_icom_devices(active_ips)
    
    # 4. Résultats
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS:")
    
    if icom_candidates:
        print(f"🎉 {len(icom_candidates)} récepteur(s) ICOM potentiel(s) trouvé(s):")
        for ip in icom_candidates:
            print(f"   🎛️  {ip}:50001")
        
        print(f"\n🔧 Pour utiliser le récepteur, modifiez backend/main.py:")
        print(f"   udp_host=\"{icom_candidates[0]}\"")
        
    else:
        print("❌ Aucun récepteur ICOM trouvé")
        print("\n💡 Vérifications à faire:")
        print("   1. Récepteur allumé et connecté via RJ45")
        print("   2. Menu récepteur → Network → CI-V Enable = ON")
        print("   3. Port CI-V = 50001")
        print("   4. Remote Control = ON")
    
    print("\n📋 Tous les appareils actifs:")
    for ip in sorted(active_ips):
        print(f"   📱 {ip}")

if __name__ == "__main__":
    main()
