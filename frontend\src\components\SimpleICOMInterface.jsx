import React, { useState, useEffect, useRef } from 'react';
import { 
  Power, Volume2, Mic, Play, Square, RotateCw, 
  Plus, Minus, Radio, Signal, Headphones
} from 'lucide-react';
import axios from 'axios';

const SimpleICOMInterface = () => {
  // États essentiels
  const [powerOn, setPowerOn] = useState(false);
  const [frequency, setFrequency] = useState(145500000); // 145.500 MHz
  const [mode, setMode] = useState('FM');
  const [volume, setVolume] = useState(50);
  const [squelch, setSquelch] = useState(10);
  const [isListening, setIsListening] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [signalLevel, setSignalLevel] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');

  // Configuration API
  const API_BASE_URL = 'http://localhost:8000';

  // Modes principaux utilisés
  const modes = ['FM', 'AM', 'USB', 'LSB', 'WFM'];

  // Fréquences courantes pour opérateur
  const quickFrequencies = [
    { name: "2m Repeater", freq: 145500000, mode: "FM" },
    { name: "70cm Repeater", freq: 433500000, mode: "FM" },
    { name: "Aviation", freq: 121500000, mode: "AM" },
    { name: "Marine Ch16", freq: 156800000, mode: "FM" },
    { name: "Police", freq: 162000000, mode: "FM" },
    { name: "PMR446", freq: 446006250, mode: "FM" }
  ];

  // Formatage simple de la fréquence
  const formatFrequency = (freq) => {
    return (freq / 1000000).toFixed(3);
  };

  // Envoi de commande simplifié
  const sendCommand = async (command) => {
    if (!powerOn) return false;
    
    try {
      const response = await axios.post(`${API_BASE_URL}/api/command`, command, {
        timeout: 3000
      });
      
      if (response.data.success) {
        setConnectionStatus('connected');
        return true;
      } else {
        console.log('Commande envoyée mais erreur:', response.data.message);
        setConnectionStatus('error');
        return false;
      }
    } catch (error) {
      console.error('Erreur connexion:', error.message);
      setConnectionStatus('error');
      return false;
    }
  };

  // Changer la fréquence
  const changeFrequency = async (newFreq) => {
    setFrequency(newFreq);
    if (powerOn) {
      await sendCommand({ frequency: newFreq });
    }
  };

  // Changer le mode
  const changeMode = async (newMode) => {
    setMode(newMode);
    if (powerOn) {
      await sendCommand({ mode: newMode });
    }
  };

  // Ajuster la fréquence par pas
  const adjustFrequency = (step) => {
    const newFreq = Math.max(100000, Math.min(3000000000, frequency + step));
    changeFrequency(newFreq);
  };

  // Sélectionner une fréquence rapide
  const selectQuickFreq = (quickFreq) => {
    changeFrequency(quickFreq.freq);
    changeMode(quickFreq.mode);
    
    // Démarrer automatiquement l'écoute
    if (!isListening) {
      startListening();
    }
  };

  // Démarrer l'écoute
  const startListening = async () => {
    if (!powerOn) return;
    
    try {
      await axios.post(`${API_BASE_URL}/api/audio/start`, {
        audio_type: 'AF',
        frequency: frequency
      });
      setIsListening(true);
    } catch (error) {
      console.error('Erreur démarrage écoute:', error);
    }
  };

  // Arrêter l'écoute
  const stopListening = async () => {
    try {
      await axios.post(`${API_BASE_URL}/api/audio/stop`);
      setIsListening(false);
    } catch (error) {
      console.error('Erreur arrêt écoute:', error);
    }
  };

  // Démarrer l'enregistrement
  const startRecording = async () => {
    if (!powerOn) return;
    
    try {
      await axios.post(`${API_BASE_URL}/api/audio/start`, {
        audio_type: 'AF',
        frequency: frequency,
        record: true
      });
      setIsRecording(true);
    } catch (error) {
      console.error('Erreur démarrage enregistrement:', error);
    }
  };

  // Arrêter l'enregistrement
  const stopRecording = async () => {
    try {
      await axios.post(`${API_BASE_URL}/api/audio/stop`);
      setIsRecording(false);
    } catch (error) {
      console.error('Erreur arrêt enregistrement:', error);
    }
  };

  // Allumer/éteindre
  const togglePower = async () => {
    const newPowerState = !powerOn;
    setPowerOn(newPowerState);
    
    if (newPowerState) {
      // Envoyer la configuration initiale
      setTimeout(async () => {
        await sendCommand({
          frequency: frequency,
          mode: mode,
          volume: volume,
          squelch: squelch
        });
      }, 1000);
    } else {
      // Arrêter tout
      setIsListening(false);
      setIsRecording(false);
      setConnectionStatus('disconnected');
    }
  };

  // Simulation du signal
  useEffect(() => {
    if (powerOn && isListening) {
      const interval = setInterval(() => {
        const newLevel = Math.random() * 9;
        setSignalLevel(newLevel);
      }, 500);
      
      return () => clearInterval(interval);
    } else {
      setSignalLevel(0);
    }
  }, [powerOn, isListening]);

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      {/* En-tête simple */}
      <div className="text-center mb-6">
        <h1 className="text-3xl font-bold text-blue-400 mb-2">ICOM IC-R8600</h1>
        <div className="flex items-center justify-center space-x-4">
          <div className={`flex items-center space-x-2 ${
            connectionStatus === 'connected' ? 'text-green-400' : 
            connectionStatus === 'error' ? 'text-red-400' : 'text-gray-400'
          }`}>
            <Signal size={16} />
            <span className="text-sm">
              {connectionStatus === 'connected' ? 'Connecté' : 
               connectionStatus === 'error' ? 'Erreur' : 'Déconnecté'}
            </span>
          </div>
        </div>
      </div>

      {/* Interface principale */}
      <div className="max-w-4xl mx-auto">
        
        {/* Écran principal */}
        <div className="bg-black p-6 rounded-lg mb-6 border-2 border-gray-600">
          <div className="bg-green-900 p-4 rounded border border-green-600">
            {/* Fréquence principale */}
            <div className="text-center mb-4">
              <div className="text-5xl font-mono font-bold text-green-400 tracking-wider">
                {powerOn ? formatFrequency(frequency) : "----.---"}
                <span className="text-2xl ml-2">MHz</span>
              </div>
            </div>
            
            {/* Informations de statut */}
            <div className="flex justify-between items-center text-lg">
              <div className="flex items-center space-x-4">
                <span className="bg-green-800 px-3 py-1 rounded text-green-200 font-bold">
                  {powerOn ? mode : "---"}
                </span>
                {isListening && (
                  <span className="bg-blue-800 px-3 py-1 rounded text-blue-200 animate-pulse">
                    ♪ ÉCOUTE
                  </span>
                )}
                {isRecording && (
                  <span className="bg-red-800 px-3 py-1 rounded text-red-200 animate-pulse">
                    ● REC
                  </span>
                )}
              </div>
              <div className="text-green-400 text-base">
                {new Date().toLocaleTimeString()}
              </div>
            </div>
            
            {/* S-meter simple */}
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Signal</span>
                <span>S{Math.floor(signalLevel)}</span>
              </div>
              <div className="flex space-x-1">
                {Array.from({ length: 9 }, (_, i) => (
                  <div
                    key={i}
                    className={`h-3 flex-1 border border-green-600 ${
                      powerOn && i < signalLevel ? 'bg-green-400' : 'bg-gray-700'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Contrôles principaux */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          
          {/* Contrôles de fréquence */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-bold text-blue-400 mb-4">Fréquence</h3>
            
            {/* Saisie directe */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Fréquence (MHz)</label>
              <input
                type="number"
                value={frequency / 1000000}
                onChange={(e) => changeFrequency(parseFloat(e.target.value) * 1000000 || 0)}
                disabled={!powerOn}
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded text-white text-lg font-mono focus:border-blue-500 focus:outline-none disabled:bg-gray-800"
                step="0.001"
                min="0.1"
                max="3000"
              />
            </div>

            {/* Boutons de pas */}
            <div className="grid grid-cols-4 gap-2 mb-4">
              <button
                onClick={() => adjustFrequency(-1000000)}
                disabled={!powerOn}
                className="px-3 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 rounded transition-colors"
              >
                -1MHz
              </button>
              <button
                onClick={() => adjustFrequency(-25000)}
                disabled={!powerOn}
                className="px-3 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 rounded transition-colors"
              >
                -25k
              </button>
              <button
                onClick={() => adjustFrequency(25000)}
                disabled={!powerOn}
                className="px-3 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded transition-colors"
              >
                +25k
              </button>
              <button
                onClick={() => adjustFrequency(1000000)}
                disabled={!powerOn}
                className="px-3 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded transition-colors"
              >
                +1MHz
              </button>
            </div>

            {/* Modes */}
            <div>
              <label className="block text-sm font-medium mb-2">Mode</label>
              <div className="grid grid-cols-5 gap-2">
                {modes.map(m => (
                  <button
                    key={m}
                    onClick={() => changeMode(m)}
                    disabled={!powerOn}
                    className={`px-3 py-2 rounded font-medium transition-colors ${
                      mode === m 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800'
                    }`}
                  >
                    {m}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Contrôles audio */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-bold text-blue-400 mb-4">Audio</h3>
            
            {/* Volume */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Volume: {volume}%</label>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => setVolume(parseInt(e.target.value))}
                disabled={!powerOn}
                className="w-full"
              />
            </div>

            {/* Squelch */}
            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">Squelch: {squelch}%</label>
              <input
                type="range"
                min="0"
                max="100"
                value={squelch}
                onChange={(e) => setSquelch(parseInt(e.target.value))}
                disabled={!powerOn}
                className="w-full"
              />
            </div>

            {/* Boutons audio */}
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={isListening ? stopListening : startListening}
                disabled={!powerOn}
                className={`flex items-center justify-center px-4 py-3 rounded font-medium transition-colors ${
                  isListening
                    ? 'bg-yellow-600 hover:bg-yellow-700'
                    : 'bg-green-600 hover:bg-green-700'
                } disabled:bg-gray-600`}
              >
                {isListening ? <Square size={20} className="mr-2" /> : <Headphones size={20} className="mr-2" />}
                {isListening ? 'Arrêter' : 'Écouter'}
              </button>

              <button
                onClick={isRecording ? stopRecording : startRecording}
                disabled={!powerOn}
                className={`flex items-center justify-center px-4 py-3 rounded font-medium transition-colors ${
                  isRecording
                    ? 'bg-red-600 hover:bg-red-700'
                    : 'bg-blue-600 hover:bg-blue-700'
                } disabled:bg-gray-600`}
              >
                {isRecording ? <Square size={20} className="mr-2" /> : <Mic size={20} className="mr-2" />}
                {isRecording ? 'Stop Rec' : 'Enregistrer'}
              </button>
            </div>
          </div>
        </div>

        {/* Fréquences rapides */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-bold text-blue-400 mb-4">Fréquences Rapides</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {quickFrequencies.map((freq, index) => (
              <button
                key={index}
                onClick={() => selectQuickFreq(freq)}
                disabled={!powerOn}
                className="text-left px-4 py-3 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 rounded transition-colors"
              >
                <div className="font-medium text-white">{freq.name}</div>
                <div className="text-sm text-blue-400">
                  {formatFrequency(freq.freq)} MHz - {freq.mode}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Bouton d'alimentation */}
        <div className="text-center">
          <button
            onClick={togglePower}
            className={`flex items-center justify-center px-12 py-4 rounded-lg font-bold text-xl transition-all mx-auto ${
              powerOn
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            <Power size={28} className="mr-3" />
            {powerOn ? 'ÉTEINDRE' : 'ALLUMER'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleICOMInterface;
