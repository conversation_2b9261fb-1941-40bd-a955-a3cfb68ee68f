"""
Serveur FastAPI professionnel pour ICOM IC-R8600
Intégration complète: WebSocket, monitoring, base de données, audio streaming
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles

# Imports des modules personnalisés
from models import (
    CommandRequest, CommandResponse, RadioStatusResponse,
    ScanRequest, AudioRecordingRequest, AudioRecordingResponse,
    AudioRecordingStatus, RecordingInfo, AudioDevice, ErrorResponse
)
from ultra_fast_network import UltraFastICOMHandler, UltraFastConfig
from database import ICOMDatabase, FrequencyPreset, CommunicationLog
from websocket_manager import websocket_manager, MessageType
from monitoring import monitoring_system, ComponentType, LogLevel, monitor_performance
from audio_recorder import AudioRecorder

# Configuration globale
class AppConfig:
    ICOM_HOST = "*************"
    ICOM_PORT = 50001
    DATABASE_PATH = "icom_r8600_professional.db"
    AUDIO_SAMPLE_RATE = 48000
    MAX_WEBSOCKET_CONNECTIONS = 50
    MONITORING_ENABLED = True

# Variables globales
icom_handler: Optional[UltraFastICOMHandler] = None
database: Optional[ICOMDatabase] = None
audio_recorder: Optional[AudioRecorder] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gestionnaire de cycle de vie de l'application"""
    global icom_handler, database, audio_recorder
    
    # === DÉMARRAGE ===
    monitoring_system.log_event(
        ComponentType.SYSTEM, "startup_begin", LogLevel.INFO,
        "Démarrage du serveur professionnel ICOM R8600"
    )
    
    try:
        # Initialiser la base de données
        database = ICOMDatabase(AppConfig.DATABASE_PATH)
        monitoring_system.log_event(
            ComponentType.DATABASE, "initialized", LogLevel.INFO,
            "Base de données initialisée"
        )
        
        # Initialiser le handler ICOM ultra-rapide
        config = UltraFastConfig(
            host=AppConfig.ICOM_HOST,
            port=AppConfig.ICOM_PORT,
            timeout=0.02  # 20ms timeout
        )
        
        icom_handler = UltraFastICOMHandler(
            config=config,
            status_callback=_on_radio_status_change
        )
        
        # Connexion au récepteur
        if await icom_handler.connect():
            monitoring_system.log_event(
                ComponentType.RADIO, "connected", LogLevel.INFO,
                f"Connexion ICOM établie ({AppConfig.ICOM_HOST}:{AppConfig.ICOM_PORT})"
            )
        else:
            monitoring_system.log_event(
                ComponentType.RADIO, "connection_failed", LogLevel.WARNING,
                "Connexion ICOM échouée, mode simulation activé"
            )
        
        # Initialiser l'enregistreur audio
        audio_recorder = AudioRecorder(
            sample_rate=AppConfig.AUDIO_SAMPLE_RATE,
            recordings_dir="recordings"
        )
        monitoring_system.log_event(
            ComponentType.AUDIO, "initialized", LogLevel.INFO,
            "Enregistreur audio initialisé"
        )
        
        # Démarrer le monitoring
        if AppConfig.MONITORING_ENABLED:
            monitoring_system.start_monitoring()
            monitoring_system.register_alert_callback(_on_monitoring_alert)
        
        # Démarrer les tâches de fond
        asyncio.create_task(_status_update_worker())
        asyncio.create_task(_cleanup_worker())
        
        monitoring_system.log_event(
            ComponentType.SYSTEM, "startup_complete", LogLevel.INFO,
            "Serveur professionnel démarré avec succès"
        )
        
        yield
        
    except Exception as e:
        monitoring_system.log_event(
            ComponentType.SYSTEM, "startup_error", LogLevel.CRITICAL,
            f"Erreur critique au démarrage: {e}"
        )
        raise
    
    # === ARRÊT ===
    monitoring_system.log_event(
        ComponentType.SYSTEM, "shutdown_begin", LogLevel.INFO,
        "Arrêt du serveur en cours"
    )
    
    try:
        # Arrêter le monitoring
        if AppConfig.MONITORING_ENABLED:
            monitoring_system.stop_monitoring()
        
        # Fermer les connexions
        if icom_handler:
            await icom_handler.disconnect()
        
        if audio_recorder and audio_recorder.is_recording:
            audio_recorder.stop_recording()
        
        monitoring_system.log_event(
            ComponentType.SYSTEM, "shutdown_complete", LogLevel.INFO,
            "Serveur arrêté proprement"
        )
        
    except Exception as e:
        monitoring_system.log_event(
            ComponentType.SYSTEM, "shutdown_error", LogLevel.ERROR,
            f"Erreur lors de l'arrêt: {e}"
        )

# Création de l'application FastAPI
app = FastAPI(
    title="ICOM IC-R8600 Professional Controller",
    description="Interface professionnelle pour contrôle ICOM IC-R8600 avec monitoring temps réel",
    version="2.0.0",
    lifespan=lifespan
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Callbacks pour événements
async def _on_radio_status_change(status: Dict[str, Any]):
    """Callback pour changements d'état du radio"""
    await websocket_manager.broadcast_radio_status(status)
    
    # Enregistrer dans la base de données si fréquence change
    if 'frequency' in status and database:
        try:
            database.update_frequency_usage(status['frequency'])
        except Exception as e:
            monitoring_system.log_event(
                ComponentType.DATABASE, "update_error", LogLevel.ERROR,
                f"Erreur mise à jour usage fréquence: {e}"
            )

async def _on_monitoring_alert(alert: Dict[str, Any]):
    """Callback pour alertes de monitoring"""
    await websocket_manager.broadcast(MessageType.ERROR, {
        "type": "monitoring_alert",
        "message": alert['message'],
        "level": alert['level']
    })

# Tâches de fond
async def _status_update_worker():
    """Worker pour mise à jour périodique du statut"""
    while True:
        try:
            if icom_handler:
                # Obtenir les statistiques de performance
                stats = icom_handler.get_stats()
                
                # Diffuser via WebSocket
                await websocket_manager.broadcast(MessageType.STATUS_UPDATE, {
                    "performance": stats,
                    "timestamp": time.time()
                })
            
            await asyncio.sleep(5)  # Mise à jour toutes les 5 secondes
            
        except Exception as e:
            monitoring_system.log_event(
                ComponentType.SYSTEM, "status_worker_error", LogLevel.ERROR,
                f"Erreur status worker: {e}"
            )
            await asyncio.sleep(10)

async def _cleanup_worker():
    """Worker pour nettoyage périodique"""
    while True:
        try:
            await asyncio.sleep(3600)  # Toutes les heures
            
            if database:
                # Nettoyer les anciens logs (garder 30 jours)
                database.cleanup_old_logs(days_to_keep=30)
                
                monitoring_system.log_event(
                    ComponentType.DATABASE, "cleanup_completed", LogLevel.INFO,
                    "Nettoyage automatique de la base de données effectué"
                )
            
        except Exception as e:
            monitoring_system.log_event(
                ComponentType.SYSTEM, "cleanup_error", LogLevel.ERROR,
                f"Erreur nettoyage: {e}"
            )

# === ROUTES API ===

@app.get("/", response_model=dict)
async def root():
    """Page d'accueil de l'API"""
    uptime = time.time() - monitoring_system.start_time
    return {
        "name": "ICOM IC-R8600 Professional Controller",
        "version": "2.0.0",
        "status": "running",
        "uptime_hours": round(uptime / 3600, 2),
        "features": [
            "Ultra-fast network communication",
            "Real-time WebSocket",
            "SQLite database",
            "Audio streaming",
            "Performance monitoring",
            "Professional interface"
        ],
        "endpoints": {
            "docs": "/docs",
            "websocket": "/ws",
            "monitoring": "/api/monitoring"
        }
    }

@app.post("/api/command", response_model=CommandResponse)
@monitor_performance(ComponentType.RADIO, "command_execution")
async def send_command(command: CommandRequest):
    """Envoie une commande au récepteur ICOM"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")
    
    try:
        results = []
        
        # Traitement des commandes avec handler ultra-rapide
        if command.power_on is not None:
            from ultra_fast_network import build_power_command
            cmd = build_power_command(command.power_on)
            response = await icom_handler.send_command_fast(cmd)
            results.append(f"Power {'ON' if command.power_on else 'OFF'}: {'OK' if response else 'ERREUR'}")
        
        if command.frequency is not None:
            from ultra_fast_network import build_frequency_command
            cmd = build_frequency_command(command.frequency)
            response = await icom_handler.send_command_fast(cmd)
            results.append(f"Fréquence {command.frequency} Hz: {'OK' if response else 'ERREUR'}")
            
            # Enregistrer dans la base de données
            if response and database:
                database.update_frequency_usage(command.frequency)
        
        if command.mode is not None:
            from ultra_fast_network import build_mode_command
            cmd = build_mode_command(command.mode)
            response = await icom_handler.send_command_fast(cmd)
            results.append(f"Mode {command.mode}: {'OK' if response else 'ERREUR'}")
        
        # Diffuser les changements via WebSocket
        await websocket_manager.broadcast(MessageType.COMMAND_RESULT, {
            "command": command.dict(),
            "results": results,
            "timestamp": time.time()
        })
        
        return CommandResponse(
            success=True,
            message="; ".join(results) if results else "Aucune commande spécifiée",
            data={"timestamp": datetime.now().isoformat()}
        )
        
    except Exception as e:
        monitoring_system.log_event(
            ComponentType.RADIO, "command_error", LogLevel.ERROR,
            f"Erreur commande: {e}",
            {"command": command.dict()}
        )
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status", response_model=RadioStatusResponse)
@monitor_performance(ComponentType.RADIO, "status_read")
async def get_status():
    """Récupère l'état actuel du récepteur"""
    if not icom_handler:
        raise HTTPException(status_code=500, detail="Handler ICOM non initialisé")
    
    try:
        # Obtenir les statistiques de performance
        stats = icom_handler.get_stats()
        
        # Simuler le statut radio (à remplacer par vraie lecture)
        return RadioStatusResponse(
            frequency=145000000,
            mode="FM",
            rssi=-80,
            power_on=True,
            rf_gain=50,
            filter_width=15000,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        monitoring_system.log_event(
            ComponentType.RADIO, "status_error", LogLevel.ERROR,
            f"Erreur lecture statut: {e}"
        )
        raise HTTPException(status_code=500, detail=str(e))

# === ROUTES WEBSOCKET ===

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Point d'entrée WebSocket principal"""
    from websocket_manager import websocket_endpoint as ws_handler
    await ws_handler(websocket)

# === ROUTES MONITORING ===

@app.get("/api/monitoring/metrics")
async def get_monitoring_metrics():
    """Récupère les métriques de monitoring"""
    return monitoring_system.get_metrics_summary()

@app.get("/api/monitoring/events")
async def get_monitoring_events(
    component: Optional[str] = None,
    level: Optional[str] = None,
    hours_back: int = 24
):
    """Récupère les événements de monitoring"""
    return monitoring_system.get_events(component, level, hours_back)

@app.post("/api/monitoring/export")
async def export_monitoring_logs(hours_back: int = 24):
    """Exporte les logs de monitoring"""
    filename = f"monitoring_export_{int(time.time())}.json"
    
    if monitoring_system.export_logs(filename, hours_back):
        return {"success": True, "filename": filename}
    else:
        raise HTTPException(status_code=500, detail="Erreur export logs")

# === ROUTES BASE DE DONNÉES ===

@app.get("/api/presets", response_model=List[dict])
async def get_frequency_presets(category: Optional[str] = None):
    """Récupère les fréquences favorites"""
    if not database:
        raise HTTPException(status_code=500, detail="Base de données non initialisée")
    
    try:
        presets = database.get_frequency_presets(category)
        return [preset.__dict__ for preset in presets]
    except Exception as e:
        monitoring_system.log_event(
            ComponentType.DATABASE, "read_error", LogLevel.ERROR,
            f"Erreur lecture presets: {e}"
        )
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/presets")
async def add_frequency_preset(preset_data: dict):
    """Ajoute une fréquence favorite"""
    if not database:
        raise HTTPException(status_code=500, detail="Base de données non initialisée")
    
    try:
        preset = FrequencyPreset(**preset_data)
        preset_id = database.add_frequency_preset(preset)
        
        monitoring_system.log_event(
            ComponentType.DATABASE, "preset_added", LogLevel.INFO,
            f"Fréquence favorite ajoutée: {preset.name}",
            {"preset_id": preset_id, "frequency": preset.frequency}
        )
        
        return {"success": True, "preset_id": preset_id}
        
    except Exception as e:
        monitoring_system.log_event(
            ComponentType.DATABASE, "add_error", LogLevel.ERROR,
            f"Erreur ajout preset: {e}"
        )
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/statistics")
async def get_statistics():
    """Récupère les statistiques d'utilisation"""
    if not database:
        raise HTTPException(status_code=500, detail="Base de données non initialisée")
    
    try:
        stats = database.get_statistics()
        monitoring_stats = monitoring_system.get_metrics_summary()
        
        return {
            "database": stats,
            "monitoring": monitoring_stats,
            "timestamp": time.time()
        }
        
    except Exception as e:
        monitoring_system.log_event(
            ComponentType.DATABASE, "stats_error", LogLevel.ERROR,
            f"Erreur lecture statistiques: {e}"
        )
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    
    # Configuration optimisée pour production
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True,
        loop="asyncio"
    )
