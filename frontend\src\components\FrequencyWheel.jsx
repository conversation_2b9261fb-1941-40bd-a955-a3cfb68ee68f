import React, { useState, useRef, useEffect, useCallback } from 'react';
import { RotateCw, RotateCcw, Target, Lock, Unlock } from 'lucide-react';

const FrequencyWheel = ({ 
  frequency, 
  onFrequencyChange, 
  step = 25000, 
  minFreq = 100000, 
  maxFreq = 3000000000,
  disabled = false,
  className = ""
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isLocked, setIsLocked] = useState(false);
  const [lastAngle, setLastAngle] = useState(0);
  const [rotationAngle, setRotationAngle] = useState(0);
  const wheelRef = useRef(null);
  const centerRef = useRef({ x: 0, y: 0 });

  // Formatage de la fréquence
  const formatFrequency = useCallback((freq) => {
    if (freq >= 1000000000) {
      return `${(freq / 1000000000).toFixed(3)} GHz`;
    } else if (freq >= 1000000) {
      return `${(freq / 1000000).toFixed(3)} MHz`;
    } else if (freq >= 1000) {
      return `${(freq / 1000).toFixed(1)} kHz`;
    }
    return `${freq} Hz`;
  }, []);

  // Calcul de l'angle depuis le centre
  const calculateAngle = useCallback((clientX, clientY) => {
    const rect = wheelRef.current?.getBoundingClientRect();
    if (!rect) return 0;

    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = clientX - centerX;
    const deltaY = clientY - centerY;
    
    return Math.atan2(deltaY, deltaX) * (180 / Math.PI);
  }, []);

  // Gestion du début de rotation
  const handleMouseDown = useCallback((e) => {
    if (disabled || isLocked) return;
    
    e.preventDefault();
    setIsDragging(true);
    setLastAngle(calculateAngle(e.clientX, e.clientY));
  }, [disabled, isLocked, calculateAngle]);

  // Gestion de la rotation
  const handleMouseMove = useCallback((e) => {
    if (!isDragging || disabled || isLocked) return;

    const currentAngle = calculateAngle(e.clientX, e.clientY);
    let angleDiff = currentAngle - lastAngle;

    // Gestion du passage par 0°
    if (angleDiff > 180) angleDiff -= 360;
    if (angleDiff < -180) angleDiff += 360;

    // Sensibilité de la molette (plus petit = plus sensible)
    const sensitivity = 5;
    if (Math.abs(angleDiff) > sensitivity) {
      const direction = angleDiff > 0 ? 1 : -1;
      const newFreq = Math.max(minFreq, Math.min(maxFreq, frequency + (direction * step)));
      
      if (newFreq !== frequency) {
        onFrequencyChange(newFreq);
        setRotationAngle(prev => prev + angleDiff);
      }
      
      setLastAngle(currentAngle);
    }
  }, [isDragging, disabled, isLocked, calculateAngle, lastAngle, frequency, step, minFreq, maxFreq, onFrequencyChange]);

  // Fin de rotation
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Gestion tactile
  const handleTouchStart = useCallback((e) => {
    if (disabled || isLocked) return;
    
    e.preventDefault();
    const touch = e.touches[0];
    setIsDragging(true);
    setLastAngle(calculateAngle(touch.clientX, touch.clientY));
  }, [disabled, isLocked, calculateAngle]);

  const handleTouchMove = useCallback((e) => {
    if (!isDragging || disabled || isLocked) return;
    
    e.preventDefault();
    const touch = e.touches[0];
    handleMouseMove({ clientX: touch.clientX, clientY: touch.clientY });
  }, [isDragging, disabled, isLocked, handleMouseMove]);

  // Événements globaux
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove]);

  // Boutons de contrôle rapide
  const handleStepUp = () => {
    if (disabled || isLocked) return;
    const newFreq = Math.min(maxFreq, frequency + step);
    onFrequencyChange(newFreq);
  };

  const handleStepDown = () => {
    if (disabled || isLocked) return;
    const newFreq = Math.max(minFreq, frequency - step);
    onFrequencyChange(newFreq);
  };

  const handleFastStepUp = () => {
    if (disabled || isLocked) return;
    const newFreq = Math.min(maxFreq, frequency + (step * 10));
    onFrequencyChange(newFreq);
  };

  const handleFastStepDown = () => {
    if (disabled || isLocked) return;
    const newFreq = Math.max(minFreq, frequency - (step * 10));
    onFrequencyChange(newFreq);
  };

  return (
    <div className={`frequency-wheel-container ${className}`}>
      {/* Affichage de la fréquence */}
      <div className="text-center mb-4">
        <div className="text-3xl font-bold text-blue-600 mb-2">
          {formatFrequency(frequency)}
        </div>
        <div className="text-sm text-gray-500">
          Pas: {formatFrequency(step)}
        </div>
      </div>

      {/* Molette principale */}
      <div className="relative flex justify-center mb-4">
        <div
          ref={wheelRef}
          className={`
            relative w-48 h-48 rounded-full border-8 border-gray-300 
            bg-gradient-to-br from-gray-100 to-gray-200
            shadow-lg cursor-pointer select-none
            ${isDragging ? 'shadow-xl scale-105' : ''}
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            ${isLocked ? 'border-red-400' : 'border-blue-400'}
            transition-all duration-200
          `}
          style={{
            transform: `rotate(${rotationAngle}deg)`,
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
        >
          {/* Graduations de la molette */}
          {Array.from({ length: 24 }, (_, i) => (
            <div
              key={i}
              className="absolute w-1 bg-gray-600"
              style={{
                height: i % 6 === 0 ? '20px' : '12px',
                left: '50%',
                top: i % 6 === 0 ? '8px' : '12px',
                transformOrigin: '50% 88px',
                transform: `translateX(-50%) rotate(${i * 15}deg)`,
              }}
            />
          ))}
          
          {/* Indicateur central */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-8 h-8 bg-blue-600 rounded-full shadow-md flex items-center justify-center">
              <Target size={16} className="text-white" />
            </div>
          </div>
          
          {/* Indicateur de position */}
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
            <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-transparent border-b-red-500" />
          </div>
        </div>
      </div>

      {/* Contrôles */}
      <div className="grid grid-cols-2 gap-2 mb-4">
        {/* Boutons de pas simple */}
        <button
          onClick={handleStepDown}
          disabled={disabled || isLocked}
          className="flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <RotateCcw size={16} className="mr-2" />
          -{formatFrequency(step)}
        </button>
        
        <button
          onClick={handleStepUp}
          disabled={disabled || isLocked}
          className="flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <RotateCw size={16} className="mr-2" />
          +{formatFrequency(step)}
        </button>
      </div>

      {/* Boutons de pas rapide */}
      <div className="grid grid-cols-2 gap-2 mb-4">
        <button
          onClick={handleFastStepDown}
          disabled={disabled || isLocked}
          className="flex items-center justify-center px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
        >
          ↓↓ -{formatFrequency(step * 10)}
        </button>
        
        <button
          onClick={handleFastStepUp}
          disabled={disabled || isLocked}
          className="flex items-center justify-center px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
        >
          ↑↑ +{formatFrequency(step * 10)}
        </button>
      </div>

      {/* Verrouillage */}
      <div className="text-center">
        <button
          onClick={() => setIsLocked(!isLocked)}
          disabled={disabled}
          className={`
            flex items-center justify-center px-4 py-2 rounded-lg transition-colors
            ${isLocked 
              ? 'bg-red-500 hover:bg-red-600 text-white' 
              : 'bg-green-500 hover:bg-green-600 text-white'
            }
            disabled:opacity-50 disabled:cursor-not-allowed
          `}
        >
          {isLocked ? <Lock size={16} className="mr-2" /> : <Unlock size={16} className="mr-2" />}
          {isLocked ? 'Verrouillé' : 'Déverrouillé'}
        </button>
      </div>
    </div>
  );
};

export default FrequencyWheel;
