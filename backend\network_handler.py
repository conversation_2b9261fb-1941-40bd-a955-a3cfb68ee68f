"""
Handler réseau optimisé pour communication directe avec IC-R8600 via RJ45
Implémente le protocole réseau ICOM avec latence < 100ms et reconnexion automatique
"""

import socket
import struct
import time
import logging
import asyncio
import threading
from typing import Optional, Dict, Any, List, Callable
from dataclasses import dataclass
from enum import Enum

class ConnectionState(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"

@dataclass
class NetworkConfig:
    """Configuration réseau optimisée pour IC-R8600"""
    host: str = "*************"
    port: int = 50001
    timeout: float = 0.05  # 50ms pour latence ultra-faible
    connect_timeout: float = 2.0
    keepalive_interval: float = 1.0
    max_retries: int = 5
    retry_delay: float = 0.5
    tcp_nodelay: bool = True  # Désactiver l'algorithme de Nagle
    tcp_keepalive: bool = True
    
class ICOMNetworkHandler:
    """Handler optimisé pour communication réseau directe avec IC-R8600"""

    def __init__(self, config: NetworkConfig = None, status_callback: Callable = None):
        self.config = config or NetworkConfig()
        self.socket = None
        self.state = ConnectionState.DISCONNECTED
        self.status_callback = status_callback
        self.reconnect_thread = None
        self.keepalive_thread = None
        self.stop_threads = False
        self.last_command_time = 0
        self.command_count = 0
        self.error_count = 0

        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    @property
    def connected(self) -> bool:
        """Vérifie si la connexion est active"""
        return self.state == ConnectionState.CONNECTED and self.socket is not None
        
    def connect(self) -> bool:
        """Établit la connexion réseau optimisée avec l'IC-R8600"""
        if self.state == ConnectionState.CONNECTING:
            return False

        self.state = ConnectionState.CONNECTING
        self._notify_status_change()

        try:
            # Créer socket avec optimisations pour latence faible
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

            # Optimisations TCP pour latence minimale
            if self.config.tcp_nodelay:
                self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

            if self.config.tcp_keepalive:
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                # Configuration keepalive (Linux/Windows)
                try:
                    self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 1)
                    self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 1)
                    self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)
                except:
                    pass  # Pas supporté sur tous les OS

            # Buffers optimisés
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 8192)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 8192)

            # Timeout de connexion
            self.socket.settimeout(self.config.connect_timeout)

            self.logger.info(f"🔌 Connexion à {self.config.host}:{self.config.port}")
            start_time = time.time()
            self.socket.connect((self.config.host, self.config.port))
            connect_time = (time.time() - start_time) * 1000

            # Timeout opérationnel ultra-court
            self.socket.settimeout(self.config.timeout)

            # Handshake initial
            if self._perform_handshake():
                self.state = ConnectionState.CONNECTED
                self.error_count = 0
                self._notify_status_change()
                self._start_keepalive()
                self.logger.info(f"✅ Connexion établie en {connect_time:.1f}ms")
                return True
            else:
                self.logger.error("❌ Échec du handshake")
                self._cleanup_connection()
                return False

        except Exception as e:
            self.logger.error(f"❌ Erreur connexion: {e}")
            self.error_count += 1
            self._cleanup_connection()
            return False
    
    def disconnect(self):
        """Ferme la connexion réseau proprement"""
        self.stop_threads = True
        self._stop_keepalive()
        self._cleanup_connection()
        self.logger.info("🔌 Connexion fermée")

    def _cleanup_connection(self):
        """Nettoie les ressources de connexion"""
        self.state = ConnectionState.DISCONNECTED
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        self._notify_status_change()

    def _notify_status_change(self):
        """Notifie les changements d'état via callback"""
        if self.status_callback:
            try:
                self.status_callback({
                    "state": self.state.value,
                    "connected": self.connected,
                    "error_count": self.error_count,
                    "command_count": self.command_count
                })
            except Exception as e:
                self.logger.error(f"Erreur callback status: {e}")

    def _start_keepalive(self):
        """Démarre le thread de keepalive"""
        if not self.keepalive_thread or not self.keepalive_thread.is_alive():
            self.stop_threads = False
            self.keepalive_thread = threading.Thread(target=self._keepalive_worker, daemon=True)
            self.keepalive_thread.start()

    def _stop_keepalive(self):
        """Arrête le thread de keepalive"""
        self.stop_threads = True
        if self.keepalive_thread and self.keepalive_thread.is_alive():
            self.keepalive_thread.join(timeout=1.0)

    def _keepalive_worker(self):
        """Worker thread pour maintenir la connexion"""
        while not self.stop_threads and self.connected:
            try:
                time.sleep(self.config.keepalive_interval)
                if not self.stop_threads:
                    # Ping simple pour vérifier la connexion
                    self._send_ping()
            except Exception as e:
                self.logger.warning(f"Keepalive error: {e}")
                if self.connected:
                    self._trigger_reconnect()
                break

    def _send_ping(self) -> bool:
        """Envoie un ping pour vérifier la connexion"""
        try:
            ping_packet = self._build_ping_packet()
            return self._send_command_raw(ping_packet) is not None
        except:
            return False

    def _trigger_reconnect(self):
        """Déclenche une reconnexion automatique"""
        if self.state != ConnectionState.RECONNECTING:
            self.state = ConnectionState.RECONNECTING
            self._notify_status_change()
            if not self.reconnect_thread or not self.reconnect_thread.is_alive():
                self.reconnect_thread = threading.Thread(target=self._reconnect_worker, daemon=True)
                self.reconnect_thread.start()

    def _reconnect_worker(self):
        """Worker thread pour reconnexion automatique"""
        retry_count = 0
        while retry_count < self.config.max_retries and not self.stop_threads:
            self.logger.info(f"🔄 Tentative de reconnexion {retry_count + 1}/{self.config.max_retries}")

            self._cleanup_connection()
            time.sleep(self.config.retry_delay)

            if self.connect():
                self.logger.info("✅ Reconnexion réussie")
                return

            retry_count += 1

        self.logger.error("❌ Échec de reconnexion après toutes les tentatives")
        self.state = ConnectionState.ERROR
        self._notify_status_change()
    
    def _perform_handshake(self) -> bool:
        """Effectue le handshake initial optimisé avec l'IC-R8600"""
        try:
            start_time = time.time()

            # Séquence d'initialisation CI-V pour IC-R8600
            init_packet = self._build_init_packet()
            self.socket.send(init_packet)

            # Attendre la réponse avec timeout court
            response = self.socket.recv(1024)

            handshake_time = (time.time() - start_time) * 1000

            if response and len(response) > 0:
                self.logger.info(f"🤝 Handshake réussi en {handshake_time:.1f}ms: {response.hex()}")
                return True
            else:
                self.logger.warning("⚠️ Pas de réponse au handshake, mode simulation")
                return True  # Continuer en mode simulation pour les tests

        except socket.timeout:
            self.logger.warning("⏱️ Timeout handshake, mode simulation")
            return True  # Continuer en mode simulation
        except Exception as e:
            self.logger.error(f"❌ Erreur handshake: {e}")
            return False
    
    def _build_init_packet(self) -> bytes:
        """Construit le paquet d'initialisation CI-V pour IC-R8600"""
        # Commande CI-V de ping/test pour IC-R8600
        # FE FE 96 E0 FD (ping vers IC-R8600)
        return bytes([0xFE, 0xFE, 0x96, 0xE0, 0xFD])

    def _build_ping_packet(self) -> bytes:
        """Construit un paquet de ping pour keepalive"""
        # Commande de lecture de fréquence (rapide et non-intrusive)
        return bytes([0xFE, 0xFE, 0x96, 0xE0, 0x03, 0xFD])
    
    def _build_command_packet(self, command_type: int, data: bytes = b'') -> bytes:
        """Construit un paquet de commande CI-V"""
        # Pour IC-R8600, utiliser le protocole CI-V standard
        # FE FE [radio_addr] [controller_addr] [command] [data] FD
        cmd = [0xFE, 0xFE, 0x96, 0xE0, command_type] + list(data) + [0xFD]
        return bytes(cmd)

    def _send_command_raw(self, packet: bytes) -> Optional[bytes]:
        """Envoie une commande brute et lit la réponse"""
        if not self.connected or not self.socket:
            self.logger.warning("Pas de connexion pour envoyer la commande")
            return None

        try:
            start_time = time.time()
            self.socket.send(packet)

            # Lecture avec timeout ultra-court
            response = self.socket.recv(1024)

            latency = (time.time() - start_time) * 1000
            self.last_command_time = latency
            self.command_count += 1

            if latency > 100:
                self.logger.warning(f"⚠️ Latence élevée: {latency:.1f}ms")

            return response if response else None

        except socket.timeout:
            self.logger.warning("⏱️ Timeout commande")
            return None
        except Exception as e:
            self.logger.error(f"❌ Erreur envoi commande: {e}")
            self.error_count += 1
            if self.connected:
                self._trigger_reconnect()
            return None

    def _send_command(self, command_type: int, data: bytes = b'') -> Optional[bytes]:
        """Envoie une commande CI-V et lit la réponse"""
        packet = self._build_command_packet(command_type, data)
        return self._send_command_raw(packet)

    def get_connection_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques de connexion"""
        return {
            "state": self.state.value,
            "connected": self.connected,
            "command_count": self.command_count,
            "error_count": self.error_count,
            "last_latency_ms": self.last_command_time,
            "host": self.config.host,
            "port": self.config.port
        }
    
    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence via réseau"""
        try:
            # Conversion fréquence en format réseau ICOM
            freq_data = struct.pack('>Q', freq_hz)  # 8 bytes, big-endian
            packet = self._build_command_packet(0x0001, freq_data)
            
            response = self._send_command(packet)
            
            if response:
                self.logger.info(f"Fréquence {freq_hz} Hz définie")
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
        
        return False
    
    def set_mode(self, mode: str) -> bool:
        """Définit le mode de modulation"""
        mode_map = {
            "LSB": 0x00, "USB": 0x01, "AM": 0x02, "CW": 0x03,
            "FM": 0x05, "WFM": 0x06, "CWR": 0x07, "RTTY": 0x08
        }
        
        if mode not in mode_map:
            return False
        
        try:
            mode_data = struct.pack('B', mode_map[mode])
            packet = self._build_command_packet(0x0002, mode_data)
            
            response = self._send_command(packet)
            
            if response:
                self.logger.info(f"Mode {mode} défini")
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur set_mode: {e}")
        
        return False
    
    def power_control(self, power_on: bool) -> bool:
        """Contrôle l'alimentation"""
        try:
            power_data = struct.pack('B', 1 if power_on else 0)
            packet = self._build_command_packet(0x0003, power_data)
            
            response = self._send_command(packet)
            
            if response:
                state = "ON" if power_on else "OFF"
                self.logger.info(f"Alimentation {state}")
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur power_control: {e}")
        
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état du récepteur"""
        try:
            packet = self._build_command_packet(0x0010)  # Commande status
            response = self._send_command(packet)
            
            if response and len(response) >= 16:
                # Décodage de la réponse (à adapter selon le protocole)
                freq = struct.unpack('>Q', response[8:16])[0]
                mode_byte = response[16] if len(response) > 16 else 0
                
                return {
                    "frequency": freq,
                    "mode": self._decode_mode(mode_byte),
                    "power_on": True,
                    "connected": True
                }
                
        except Exception as e:
            self.logger.error(f"Erreur get_status: {e}")
        
        return {
            "frequency": 0,
            "mode": "FM",
            "power_on": False,
            "connected": self.connected
        }
    
    def _decode_mode(self, mode_byte: int) -> str:
        """Décode le mode depuis la réponse"""
        mode_map = {
            0x00: "LSB", 0x01: "USB", 0x02: "AM", 0x03: "CW",
            0x05: "FM", 0x06: "WFM", 0x07: "CWR", 0x08: "RTTY"
        }
        return mode_map.get(mode_byte, "FM")

class ICOMNetworkBridge:
    """Bridge entre l'API REST et le handler réseau"""
    
    def __init__(self, network_config: NetworkConfig = None):
        self.network_handler = ICOMNetworkHandler(network_config)
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self) -> bool:
        """Initialise la connexion réseau"""
        return self.network_handler.connect()
    
    async def cleanup(self):
        """Nettoie les ressources"""
        self.network_handler.disconnect()
    
    async def execute_command(self, command: str, **kwargs) -> Dict[str, Any]:
        """Exécute une commande sur le récepteur"""
        try:
            if command == "set_frequency":
                success = self.network_handler.set_frequency(kwargs.get("frequency", 0))
                return {"success": success, "message": f"Fréquence définie"}
            
            elif command == "set_mode":
                success = self.network_handler.set_mode(kwargs.get("mode", "FM"))
                return {"success": success, "message": f"Mode défini"}
            
            elif command == "power_on":
                success = self.network_handler.power_control(True)
                return {"success": success, "message": "Récepteur allumé"}
            
            elif command == "power_off":
                success = self.network_handler.power_control(False)
                return {"success": success, "message": "Récepteur éteint"}
            
            elif command == "get_status":
                status = self.network_handler.get_status()
                return {"success": True, "data": status}
            
            else:
                return {"success": False, "message": "Commande inconnue"}
                
        except Exception as e:
            self.logger.error(f"Erreur execute_command: {e}")
            return {"success": False, "message": str(e)}
