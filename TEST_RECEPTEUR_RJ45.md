# 🔌 Test Communication RJ45 avec Récepteur ICOM IC-R8600

## ✅ **Backend Configuré pour RJ45**

Le backend est maintenant configuré pour communiquer **uniquement via câble RJ45** avec votre récepteur :

### 🌐 **Configuration R<PERSON>eau Active**
- **IP Récepteur** : *************
- **Port** : 50001
- **Protocole** : UDP
- **Connexion** : ✅ Connexion UDP établie vers IC-R8600
- **Mode** : Réseau uniquement (pas de USB)

---

## 🧪 **Tests à Effectuer**

### **1. Vérification de Base**
1. **Récepteur allumé** et connecté via RJ45
2. **Backend actif** : `python main.py`
3. **Interface web** : http://localhost:5173
4. **Logs backend** : Connexion UDP établie ✅

### **2. Test Changement de Fréquence**
1. **Noter** la fréquence actuelle sur le récepteur
2. **Interface web** : C<PERSON>r "ALLUMER"
3. **Saisir** une nouvelle fréquence (ex: 145.500)
4. **Observer** : Le récepteur doit changer de fréquence
5. **Vérifier** : Affichage du récepteur = interface web

### **3. Test Changement de Mode**
1. **Noter** le mode actuel sur le récepteur
2. **Interface web** : Cliquer sur un mode (FM → AM)
3. **Observer** : Le récepteur doit changer de mode
4. **Vérifier** : Mode du récepteur = interface web

### **4. Test Fréquences Rapides**
1. **Cliquer** "Police" (162.000 MHz FM)
2. **Observer** : Récepteur → 162.000 MHz + mode FM
3. **Cliquer** "Aviation" (121.500 MHz AM)
4. **Observer** : Récepteur → 121.500 MHz + mode AM

---

## 📊 **Logs à Observer**

### **Démarrage Backend**
```
INFO - ✅ Connexion UDP établie vers IC-R8600 *************:50001
INFO - Connexion ICOM établie
INFO - Enregistreur audio initialisé
```

### **Commandes Envoyées**
Quand vous cliquez sur l'interface, vous devriez voir :
```
INFO - 🔧 Commande reçue: frequency=145500000
INFO - 📡 Envoi commande fréquence: 145.500 MHz
INFO - ✅ Commande fréquence envoyée avec succès

INFO - 🔧 Commande reçue: mode=FM
INFO - 📡 Envoi commande mode: FM
INFO - ✅ Commande mode envoyée avec succès
```

---

## 🔍 **Diagnostic des Problèmes**

### **Si le récepteur ne change pas :**

#### **1. Vérifier la Connexion Réseau**
```bash
# Tester la connectivité
ping *************

# Vérifier que le port est ouvert
telnet ************* 50001
```

#### **2. Vérifier la Configuration Récepteur**
- **Menu récepteur** → Network Settings
- **IP Address** : *************
- **CI-V Port** : 50001
- **CI-V Enable** : ON
- **Remote Control** : ON

#### **3. Vérifier les Logs Backend**
Si vous voyez des erreurs comme :
```
ERROR - Timeout envoi commande
ERROR - Connexion refusée
ERROR - Pas de réponse du récepteur
```

**Solutions :**
1. **Redémarrer** le récepteur
2. **Vérifier** le câble RJ45
3. **Vérifier** l'IP du récepteur
4. **Désactiver** le firewall temporairement

---

## 🎯 **Test Complet Étape par Étape**

### **Étape 1 : Préparation**
1. **Récepteur ICOM IC-R8600** allumé
2. **Câble RJ45** connecté PC ↔ Récepteur
3. **IP récepteur** : *************
4. **Backend** : `python main.py` (logs OK)
5. **Interface** : http://localhost:5173

### **Étape 2 : Test Basique**
1. **Interface** : Cliquer "ALLUMER"
2. **Observer** : Écran LCD interface s'allume
3. **Récepteur** : Doit rester allumé (pas d'impact)

### **Étape 3 : Test Fréquence**
1. **Noter** fréquence actuelle récepteur
2. **Interface** : Saisir 145.500 dans le champ
3. **Appuyer** Entrée
4. **Observer** : Récepteur doit passer à 145.500 MHz
5. **Logs** : Voir commande envoyée

### **Étape 4 : Test Mode**
1. **Noter** mode actuel récepteur
2. **Interface** : Cliquer bouton "AM"
3. **Observer** : Récepteur doit passer en mode AM
4. **Logs** : Voir commande mode envoyée

### **Étape 5 : Test Fréquences Rapides**
1. **Interface** : Cliquer "Aviation"
2. **Observer** : Récepteur → 121.500 MHz AM
3. **Interface** : Cliquer "Marine Ch16"
4. **Observer** : Récepteur → 156.800 MHz FM

---

## 📋 **Résultats Attendus**

### **✅ Si Tout Fonctionne**
- **Récepteur** change de fréquence quand vous changez dans l'interface
- **Récepteur** change de mode quand vous cliquez sur FM/AM/USB/LSB
- **Logs backend** montrent commandes envoyées avec succès
- **Interface** et récepteur sont synchronisés

### **❌ Si Problème de Communication**
- **Récepteur** ne change pas malgré les clics
- **Logs** montrent erreurs de timeout ou connexion
- **Interface** fonctionne mais pas d'impact sur récepteur

---

## 🔧 **Solutions Rapides**

### **Problème Réseau**
```bash
# Vérifier IP récepteur
ping *************

# Si pas de réponse, vérifier l'IP dans le menu récepteur
# Menu → Network → IP Settings
```

### **Problème Configuration**
1. **Menu récepteur** → CI-V Settings
2. **CI-V Address** : 96 (par défaut)
3. **CI-V Baud Rate** : 19200
4. **CI-V Transceive** : ON
5. **Remote Control** : ON

### **Problème Firewall**
```bash
# Désactiver temporairement le firewall Windows
# Panneau de configuration → Firewall Windows → Désactiver
```

---

## 🎉 **Test Final**

**Pour confirmer que tout fonctionne :**

1. **Démarrer** : Backend + Interface
2. **Allumer** : Interface web
3. **Cliquer** : "Police" (162.000 MHz)
4. **Vérifier** : Récepteur affiche 162.000 MHz
5. **Cliquer** : "Aviation" (121.500 MHz)
6. **Vérifier** : Récepteur affiche 121.500 MHz + mode AM

**Si ces tests passent → Communication RJ45 fonctionnelle !** ✅

**Si ces tests échouent → Problème de configuration réseau** ❌

---

## 📞 **Support**

Si les commandes n'arrivent pas au récepteur :
1. **Vérifier** les logs backend pour erreurs
2. **Tester** `ping *************`
3. **Vérifier** configuration réseau récepteur
4. **Redémarrer** récepteur et backend

**L'interface est prête - testez maintenant avec votre récepteur !** 🔌📻
