# ICOM IC-R8600 Professional Controller v2.0

## 🚀 Interface Professionnelle de Contrôle

Application web complète pour le contrôle et la surveillance du récepteur ICOM IC-R8600 via communication réseau IP, avec interface utilisateur moderne et communication en temps réel.

## ✨ Nouvelles Fonctionnalités v2.0

### 🔧 Contrôles Avancés
- ✅ **Molette de fréquence interactive** - Changement intuitif avec molette virtuelle
- ✅ **Contrôle volume et squelch** - Réglages précis en temps réel
- ✅ **Affichage LCD virtuel** - Simulation réaliste de l'écran ICOM R8600
- ✅ **Sélection des filtres** - Contrôle complet des filtres IF
- ✅ **Streaming audio live** - Écoute en temps réel même avec squelch

### 🌐 Communication Ultra-Rapide
- ✅ **Latence < 50ms** - Communication optimisée TCP/UDP
- ✅ **Reconnexion automatique** - Gestion robuste des déconnexions
- ✅ **WebSocket bidirectionnel** - Synchronisation instantanée
- ✅ **Monitoring temps réel** - Surveillance continue des performances

### 💾 Base de Données Intégrée
- ✅ **SQLite intégré** - Stockage local des données
- ✅ **Fréquences favorites** - Mémorisation et organisation
- ✅ **Historique communications** - Traçabilité complète
- ✅ **Logs détaillés** - Surveillance et débogage

### 📊 Interface Professionnelle
- ✅ **Thème sombre moderne** - Design professionnel
- ✅ **Interface responsive** - Adaptation mobile/desktop
- ✅ **Notifications temps réel** - Alertes et statuts
- ✅ **Statistiques avancées** - Métriques de performance

## 🏗️ Architecture Technique

```
icom-r8600-professional/
├── backend/
│   ├── main_professional.py      # Serveur FastAPI principal
│   ├── ultra_fast_network.py     # Communication ultra-rapide
│   ├── database.py               # Gestionnaire SQLite
│   ├── websocket_manager.py      # WebSocket temps réel
│   ├── monitoring.py             # Système de monitoring
│   ├── audio_recorder.py         # Enregistrement audio
│   └── models.py                 # Modèles de données
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── ProfessionalInterface.jsx  # Interface principale
│   │   │   ├── FrequencyWheel.jsx         # Molette de fréquence
│   │   │   ├── VirtualLCD.jsx             # Affichage LCD
│   │   │   └── AudioControls.jsx          # Contrôles audio
│   │   └── App.jsx
│   └── package.json
├── config_professional.json      # Configuration complète
├── start_professional.bat        # Script de démarrage
└── README_PROFESSIONAL.md
```

## 🚀 Installation et Démarrage

### Prérequis
- Python 3.8+
- Node.js 16+
- ICOM IC-R8600 connecté via RJ45

### Démarrage Rapide

1. **Cloner le projet**
```bash
git clone [repository]
cd icom-r8600-professional
```

2. **Lancer la version professionnelle**
```bash
# Windows
start_professional.bat

# Linux/Mac
chmod +x start_professional.sh
./start_professional.sh
```

3. **Accéder à l'interface**
- Interface principale: http://localhost:8000
- Documentation API: http://localhost:8000/docs
- Monitoring: http://localhost:8000/api/monitoring/metrics

### Installation Manuelle

#### Backend
```bash
cd backend
pip install -r requirements.txt
python main_professional.py
```

#### Frontend
```bash
cd frontend
npm install
npm run dev
```

## 🔌 Configuration Réseau

### Configuration ICOM IC-R8600
- **Adresse IP**: ************* (configurable)
- **Port**: 50001
- **Protocole**: UDP (recommandé) ou TCP
- **Timeout**: 20ms pour latence ultra-faible

### Configuration Réseau
```json
{
  "icom": {
    "host": "*************",
    "port": 50001,
    "protocol": "udp",
    "timeout_ms": 20
  }
}
```

## 📡 Fonctionnalités Détaillées

### Contrôle de Base
- **Alimentation ON/OFF** - Contrôle à distance
- **Changement de fréquence** - Molette interactive + saisie directe
- **Modes de réception** - LSB, USB, AM, CW, FM, WFM, RTTY, PSK
- **RF Gain** - Contrôle précis du gain RF
- **Volume/Squelch** - Réglages audio en temps réel

### Fonctionnalités Avancées
- **Scan automatique** - Balayage programmable
- **Fréquences favorites** - Organisation par catégories
- **Enregistrement audio** - Formats WAV, MP3, FLAC
- **Streaming live** - Écoute temps réel
- **Historique** - Traçabilité des communications

### Monitoring et Logs
- **Métriques système** - CPU, mémoire, réseau
- **Latence réseau** - Surveillance continue
- **Taux d'erreur** - Détection des problèmes
- **Alertes automatiques** - Notifications en cas de problème
- **Export des logs** - Sauvegarde et analyse

## 🎛️ Interface Utilisateur

### Affichage LCD Virtuel
- Simulation réaliste de l'écran ICOM R8600
- Affichage fréquence, mode, S-meter
- Indicateurs d'état en temps réel
- Animation et effets visuels

### Molette de Fréquence
- Rotation intuitive à la souris/tactile
- Pas de fréquence configurable
- Verrouillage de sécurité
- Boutons de pas rapide

### Contrôles Audio
- Visualiseur de niveau en temps réel
- Contrôle volume/squelch
- Streaming audio live
- Enregistrement avec métadonnées

## 📊 API REST

### Endpoints Principaux
- `POST /api/command` - Envoyer commandes
- `GET /api/status` - État du récepteur
- `GET /api/presets` - Fréquences favorites
- `GET /api/monitoring/metrics` - Métriques système
- `WebSocket /ws` - Communication temps réel

### Exemple d'utilisation
```javascript
// Changer la fréquence
fetch('/api/command', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    frequency: 145500000,
    mode: 'FM'
  })
});
```

## 🔧 Configuration Avancée

### Optimisation Performances
```json
{
  "performance": {
    "optimize_for_latency": true,
    "async_workers": 4,
    "connection_pool_size": 20,
    "cache_enabled": true
  }
}
```

### Seuils d'Alerte
```json
{
  "monitoring": {
    "alert_thresholds": {
      "cpu_percent": 80.0,
      "memory_percent": 85.0,
      "network_latency_ms": 100.0,
      "error_rate_percent": 5.0
    }
  }
}
```

## 🐛 Dépannage

### Problèmes Courants

#### Connexion Réseau
```bash
# Tester la connectivité
ping *************
telnet ************* 50001
```

#### Latence Élevée
- Vérifier la configuration réseau
- Utiliser UDP au lieu de TCP
- Réduire le timeout à 20ms

#### Erreurs WebSocket
- Vérifier les CORS
- Contrôler les logs du navigateur
- Tester la reconnexion automatique

### Logs de Debug
```bash
# Activer les logs détaillés
export LOG_LEVEL=DEBUG
python main_professional.py

# Consulter les logs
tail -f monitoring.log
tail -f icom_professional.log
```

## 🔮 Intégration Future

Cette application est conçue pour s'intégrer dans une plateforme de C2 (Commande et Contrôle) plus large :

- **Architecture modulaire** - Intégration facile
- **API standardisée** - Communication inter-modules
- **Base de données partagée** - PostgreSQL pour production
- **Authentification** - Système de sécurité centralisé
- **Géolocalisation** - Intégration cartes géographiques

## 📚 Documentation

- [Guide d'installation](docs/installation.md)
- [Configuration réseau](docs/network.md)
- [API Reference](http://localhost:8000/docs)
- [Troubleshooting](docs/troubleshooting.md)

## 🤝 Support

Pour toute question ou problème :
1. Consulter les logs de monitoring
2. Vérifier la configuration réseau
3. Tester en mode simulation
4. Contacter le support technique

---

**ICOM IC-R8600 Professional Controller v2.0**  
*Interface professionnelle pour contrôle radio avancé*
