import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Edit3, Save, X, Target, Star } from 'lucide-react';

const FrequencyManager = ({ onFrequencySelect, currentFrequency, currentMode }) => {
  const [frequencies, setFrequencies] = useState([]);
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [newFreq, setNewFreq] = useState({
    name: '',
    frequency: currentFrequency,
    mode: currentMode,
    description: '',
    category: 'Amateur'
  });

  const categories = [
    'Amateur', 'Aviation', 'Marine', 'Police', 'Pompiers', 
    'Météo', 'PMR', 'Militaire', 'Commercial', 'Autre'
  ];

  const modes = ['LSB', 'USB', 'AM', 'CW', 'FM', 'WFM', 'DV'];

  // Charger les fréquences depuis le localStorage
  useEffect(() => {
    const saved = localStorage.getItem('icom_frequencies');
    if (saved) {
      try {
        setFrequencies(JSON.parse(saved));
      } catch (error) {
        console.error('Erreur chargement fréquences:', error);
      }
    } else {
      // Fréquences par défaut
      const defaultFreqs = [
        { id: 1, name: "2m FM Repeater", frequency: 145500000, mode: "FM", description: "Relais local 2m", category: "Amateur" },
        { id: 2, name: "70cm FM Repeater", frequency: 433500000, mode: "FM", description: "Relais local 70cm", category: "Amateur" },
        { id: 3, name: "Aviation Tower", frequency: 121500000, mode: "AM", description: "Tour de contrôle", category: "Aviation" },
        { id: 4, name: "Marine VHF Ch16", frequency: 156800000, mode: "FM", description: "Canal d'urgence maritime", category: "Marine" },
        { id: 5, name: "PMR446 Ch1", frequency: 446006250, mode: "FM", description: "PMR446 Canal 1", category: "PMR" },
        { id: 6, name: "Météo France", frequency: 162550000, mode: "FM", description: "Bulletins météo", category: "Météo" }
      ];
      setFrequencies(defaultFreqs);
      localStorage.setItem('icom_frequencies', JSON.stringify(defaultFreqs));
    }
  }, []);

  // Sauvegarder dans localStorage
  const saveFrequencies = (freqs) => {
    setFrequencies(freqs);
    localStorage.setItem('icom_frequencies', JSON.stringify(freqs));
  };

  // Ajouter une nouvelle fréquence
  const addFrequency = () => {
    if (!newFreq.name || !newFreq.frequency) return;
    
    const freq = {
      id: Date.now(),
      ...newFreq,
      frequency: parseInt(newFreq.frequency)
    };
    
    const updated = [...frequencies, freq];
    saveFrequencies(updated);
    
    setNewFreq({
      name: '',
      frequency: currentFrequency,
      mode: currentMode,
      description: '',
      category: 'Amateur'
    });
    setIsAdding(false);
  };

  // Supprimer une fréquence
  const deleteFrequency = (id) => {
    const updated = frequencies.filter(f => f.id !== id);
    saveFrequencies(updated);
  };

  // Modifier une fréquence
  const updateFrequency = (id, updatedFreq) => {
    const updated = frequencies.map(f => 
      f.id === id ? { ...f, ...updatedFreq, frequency: parseInt(updatedFreq.frequency) } : f
    );
    saveFrequencies(updated);
    setEditingId(null);
  };

  // Formatage de la fréquence
  const formatFrequency = (freq) => {
    const mhz = (freq / 1000000).toFixed(6);
    return mhz.replace(/(\d{3})(\d{3})(\d{3})/, '$1.$2.$3') + ' MHz';
  };

  // Grouper par catégorie
  const groupedFrequencies = frequencies.reduce((groups, freq) => {
    const category = freq.category || 'Autre';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(freq);
    return groups;
  }, {});

  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
      {/* En-tête */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Target className="text-blue-400" size={20} />
          <h3 className="text-lg font-semibold text-white">Fréquences Cibles</h3>
        </div>
        
        <button
          onClick={() => setIsAdding(true)}
          className="flex items-center px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm transition-colors"
        >
          <Plus size={16} className="mr-1" />
          Ajouter
        </button>
      </div>

      {/* Formulaire d'ajout */}
      {isAdding && (
        <div className="bg-gray-900 rounded p-4 mb-4 border border-gray-600">
          <h4 className="text-white font-medium mb-3">Nouvelle Fréquence</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label className="block text-sm text-gray-400 mb-1">Nom</label>
              <input
                type="text"
                value={newFreq.name}
                onChange={(e) => setNewFreq({...newFreq, name: e.target.value})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded focus:border-blue-500 focus:outline-none text-white"
                placeholder="Ex: Police Locale"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-1">Fréquence (Hz)</label>
              <input
                type="number"
                value={newFreq.frequency}
                onChange={(e) => setNewFreq({...newFreq, frequency: e.target.value})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded focus:border-blue-500 focus:outline-none text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-1">Mode</label>
              <select
                value={newFreq.mode}
                onChange={(e) => setNewFreq({...newFreq, mode: e.target.value})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded focus:border-blue-500 focus:outline-none text-white"
              >
                {modes.map(mode => (
                  <option key={mode} value={mode}>{mode}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-1">Catégorie</label>
              <select
                value={newFreq.category}
                onChange={(e) => setNewFreq({...newFreq, category: e.target.value})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded focus:border-blue-500 focus:outline-none text-white"
              >
                {categories.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm text-gray-400 mb-1">Description</label>
              <input
                type="text"
                value={newFreq.description}
                onChange={(e) => setNewFreq({...newFreq, description: e.target.value})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded focus:border-blue-500 focus:outline-none text-white"
                placeholder="Description optionnelle"
              />
            </div>
          </div>
          
          <div className="flex space-x-2 mt-4">
            <button
              onClick={addFrequency}
              className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white transition-colors"
            >
              <Save size={16} className="mr-1" />
              Sauvegarder
            </button>
            <button
              onClick={() => setIsAdding(false)}
              className="flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded text-white transition-colors"
            >
              <X size={16} className="mr-1" />
              Annuler
            </button>
          </div>
        </div>
      )}

      {/* Liste des fréquences par catégorie */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {Object.entries(groupedFrequencies).map(([category, freqs]) => (
          <div key={category}>
            <h4 className="text-blue-400 font-medium mb-2 flex items-center">
              <Star size={16} className="mr-2" />
              {category} ({freqs.length})
            </h4>
            
            <div className="space-y-2">
              {freqs.map(freq => (
                <div
                  key={freq.id}
                  className="bg-gray-700 rounded p-3 hover:bg-gray-600 transition-colors cursor-pointer"
                  onClick={() => onFrequencySelect(freq)}
                >
                  {editingId === freq.id ? (
                    <EditFrequencyForm
                      frequency={freq}
                      onSave={(updated) => updateFrequency(freq.id, updated)}
                      onCancel={() => setEditingId(null)}
                      modes={modes}
                      categories={categories}
                    />
                  ) : (
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-medium text-white">{freq.name}</span>
                          <span className="text-xs px-2 py-1 bg-blue-600 rounded text-white">
                            {freq.mode}
                          </span>
                        </div>
                        <div className="text-blue-400 font-mono text-sm">
                          {formatFrequency(freq.frequency)}
                        </div>
                        {freq.description && (
                          <div className="text-gray-400 text-xs mt-1">
                            {freq.description}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex space-x-1 ml-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingId(freq.id);
                          }}
                          className="p-1 text-gray-400 hover:text-blue-400 transition-colors"
                        >
                          <Edit3 size={14} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteFrequency(freq.id);
                          }}
                          className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {frequencies.length === 0 && (
        <div className="text-center text-gray-400 py-8">
          <Target size={48} className="mx-auto mb-4 opacity-50" />
          <p>Aucune fréquence enregistrée</p>
          <p className="text-sm">Cliquez sur "Ajouter" pour commencer</p>
        </div>
      )}
    </div>
  );
};

// Composant pour éditer une fréquence
const EditFrequencyForm = ({ frequency, onSave, onCancel, modes, categories }) => {
  const [editData, setEditData] = useState(frequency);

  return (
    <div className="space-y-2" onClick={(e) => e.stopPropagation()}>
      <input
        type="text"
        value={editData.name}
        onChange={(e) => setEditData({...editData, name: e.target.value})}
        className="w-full px-2 py-1 bg-gray-800 border border-gray-600 rounded text-white text-sm"
      />
      
      <div className="grid grid-cols-2 gap-2">
        <input
          type="number"
          value={editData.frequency}
          onChange={(e) => setEditData({...editData, frequency: e.target.value})}
          className="px-2 py-1 bg-gray-800 border border-gray-600 rounded text-white text-sm"
        />
        
        <select
          value={editData.mode}
          onChange={(e) => setEditData({...editData, mode: e.target.value})}
          className="px-2 py-1 bg-gray-800 border border-gray-600 rounded text-white text-sm"
        >
          {modes.map(mode => (
            <option key={mode} value={mode}>{mode}</option>
          ))}
        </select>
      </div>
      
      <div className="flex space-x-2">
        <button
          onClick={() => onSave(editData)}
          className="flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 rounded text-white text-sm transition-colors"
        >
          <Save size={12} className="mr-1" />
          OK
        </button>
        <button
          onClick={onCancel}
          className="flex items-center px-2 py-1 bg-gray-600 hover:bg-gray-700 rounded text-white text-sm transition-colors"
        >
          <X size={12} className="mr-1" />
          Annuler
        </button>
      </div>
    </div>
  );
};

export default FrequencyManager;
