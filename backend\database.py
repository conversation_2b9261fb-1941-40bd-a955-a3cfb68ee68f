"""
Gestionnaire de base de données SQLite pour ICOM R8600
Gestion des fréquences favorites, historique et logs
"""

import sqlite3
import logging
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class FrequencyPreset:
    id: Optional[int] = None
    name: str = ""
    frequency: int = 0
    mode: str = "FM"
    description: str = ""
    category: str = "General"
    created_at: Optional[str] = None
    last_used: Optional[str] = None
    use_count: int = 0

@dataclass
class CommunicationLog:
    id: Optional[int] = None
    frequency: int = 0
    mode: str = "FM"
    timestamp: Optional[str] = None
    duration_seconds: int = 0
    signal_strength: int = -80
    notes: str = ""
    recording_file: Optional[str] = None

@dataclass
class SystemLog:
    id: Optional[int] = None
    timestamp: Optional[str] = None
    level: str = "INFO"
    component: str = ""
    message: str = ""
    details: Optional[str] = None

class ICOMDatabase:
    """Gestionnaire de base de données pour ICOM R8600"""
    
    def __init__(self, db_path: str = "icom_r8600.db"):
        self.db_path = Path(db_path)
        self.logger = logging.getLogger(__name__)
        self._init_database()
    
    def _init_database(self):
        """Initialise la base de données et crée les tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                
                # Table des fréquences favorites
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS frequency_presets (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        frequency INTEGER NOT NULL,
                        mode TEXT NOT NULL DEFAULT 'FM',
                        description TEXT DEFAULT '',
                        category TEXT DEFAULT 'General',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_used TIMESTAMP,
                        use_count INTEGER DEFAULT 0
                    )
                """)
                
                # Table de l'historique des communications
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS communication_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        frequency INTEGER NOT NULL,
                        mode TEXT NOT NULL DEFAULT 'FM',
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        duration_seconds INTEGER DEFAULT 0,
                        signal_strength INTEGER DEFAULT -80,
                        notes TEXT DEFAULT '',
                        recording_file TEXT
                    )
                """)
                
                # Table des logs système
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        level TEXT NOT NULL DEFAULT 'INFO',
                        component TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT
                    )
                """)
                
                # Index pour optimiser les requêtes
                conn.execute("CREATE INDEX IF NOT EXISTS idx_presets_frequency ON frequency_presets(frequency)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_presets_category ON frequency_presets(category)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON communication_logs(timestamp)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_logs_frequency ON communication_logs(frequency)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)")
                
                conn.commit()
                self.logger.info("✅ Base de données initialisée")
                
        except Exception as e:
            self.logger.error(f"❌ Erreur initialisation DB: {e}")
            raise
    
    # === GESTION DES FRÉQUENCES FAVORITES ===
    
    def add_frequency_preset(self, preset: FrequencyPreset) -> int:
        """Ajoute une fréquence favorite"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    INSERT INTO frequency_presets 
                    (name, frequency, mode, description, category)
                    VALUES (?, ?, ?, ?, ?)
                """, (preset.name, preset.frequency, preset.mode, 
                     preset.description, preset.category))
                
                preset_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"✅ Fréquence favorite ajoutée: {preset.name} ({preset.frequency} Hz)")
                return preset_id
                
        except Exception as e:
            self.logger.error(f"❌ Erreur ajout fréquence: {e}")
            raise
    
    def get_frequency_presets(self, category: Optional[str] = None) -> List[FrequencyPreset]:
        """Récupère les fréquences favorites"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                if category:
                    cursor = conn.execute("""
                        SELECT * FROM frequency_presets 
                        WHERE category = ? 
                        ORDER BY use_count DESC, name
                    """, (category,))
                else:
                    cursor = conn.execute("""
                        SELECT * FROM frequency_presets 
                        ORDER BY use_count DESC, name
                    """)
                
                presets = []
                for row in cursor.fetchall():
                    preset = FrequencyPreset(**dict(row))
                    presets.append(preset)
                
                return presets
                
        except Exception as e:
            self.logger.error(f"❌ Erreur lecture fréquences: {e}")
            return []
    
    def update_frequency_usage(self, frequency: int):
        """Met à jour l'utilisation d'une fréquence"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE frequency_presets 
                    SET use_count = use_count + 1, last_used = CURRENT_TIMESTAMP
                    WHERE frequency = ?
                """, (frequency,))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"❌ Erreur mise à jour usage: {e}")
    
    def delete_frequency_preset(self, preset_id: int) -> bool:
        """Supprime une fréquence favorite"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    DELETE FROM frequency_presets WHERE id = ?
                """, (preset_id,))
                
                deleted = cursor.rowcount > 0
                conn.commit()
                
                if deleted:
                    self.logger.info(f"✅ Fréquence favorite supprimée: {preset_id}")
                
                return deleted
                
        except Exception as e:
            self.logger.error(f"❌ Erreur suppression fréquence: {e}")
            return False
    
    # === GESTION DE L'HISTORIQUE ===
    
    def add_communication_log(self, log: CommunicationLog) -> int:
        """Ajoute un log de communication"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    INSERT INTO communication_logs 
                    (frequency, mode, duration_seconds, signal_strength, notes, recording_file)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (log.frequency, log.mode, log.duration_seconds,
                     log.signal_strength, log.notes, log.recording_file))
                
                log_id = cursor.lastrowid
                conn.commit()
                
                return log_id
                
        except Exception as e:
            self.logger.error(f"❌ Erreur ajout log communication: {e}")
            raise
    
    def get_communication_logs(self, 
                             frequency: Optional[int] = None,
                             hours_back: int = 24,
                             limit: int = 100) -> List[CommunicationLog]:
        """Récupère l'historique des communications"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                since_time = datetime.now() - timedelta(hours=hours_back)
                
                if frequency:
                    cursor = conn.execute("""
                        SELECT * FROM communication_logs 
                        WHERE frequency = ? AND timestamp >= ?
                        ORDER BY timestamp DESC LIMIT ?
                    """, (frequency, since_time.isoformat(), limit))
                else:
                    cursor = conn.execute("""
                        SELECT * FROM communication_logs 
                        WHERE timestamp >= ?
                        ORDER BY timestamp DESC LIMIT ?
                    """, (since_time.isoformat(), limit))
                
                logs = []
                for row in cursor.fetchall():
                    log = CommunicationLog(**dict(row))
                    logs.append(log)
                
                return logs
                
        except Exception as e:
            self.logger.error(f"❌ Erreur lecture logs: {e}")
            return []
    
    # === GESTION DES LOGS SYSTÈME ===
    
    def add_system_log(self, level: str, component: str, message: str, details: Optional[str] = None):
        """Ajoute un log système"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO system_logs (level, component, message, details)
                    VALUES (?, ?, ?, ?)
                """, (level, component, message, details))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"❌ Erreur ajout log système: {e}")
    
    def get_system_logs(self, level: Optional[str] = None, hours_back: int = 24) -> List[SystemLog]:
        """Récupère les logs système"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                since_time = datetime.now() - timedelta(hours=hours_back)
                
                if level:
                    cursor = conn.execute("""
                        SELECT * FROM system_logs 
                        WHERE level = ? AND timestamp >= ?
                        ORDER BY timestamp DESC
                    """, (level, since_time.isoformat()))
                else:
                    cursor = conn.execute("""
                        SELECT * FROM system_logs 
                        WHERE timestamp >= ?
                        ORDER BY timestamp DESC
                    """, (since_time.isoformat(),))
                
                logs = []
                for row in cursor.fetchall():
                    log = SystemLog(**dict(row))
                    logs.append(log)
                
                return logs
                
        except Exception as e:
            self.logger.error(f"❌ Erreur lecture logs système: {e}")
            return []
    
    # === STATISTIQUES ===
    
    def get_statistics(self) -> Dict[str, Any]:
        """Récupère les statistiques d'utilisation"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Nombre total de fréquences favorites
                cursor = conn.execute("SELECT COUNT(*) FROM frequency_presets")
                total_presets = cursor.fetchone()[0]
                
                # Nombre de communications dans les dernières 24h
                since_time = datetime.now() - timedelta(hours=24)
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM communication_logs 
                    WHERE timestamp >= ?
                """, (since_time.isoformat(),))
                recent_comms = cursor.fetchone()[0]
                
                # Fréquence la plus utilisée
                cursor = conn.execute("""
                    SELECT name, frequency, use_count 
                    FROM frequency_presets 
                    ORDER BY use_count DESC LIMIT 1
                """)
                most_used = cursor.fetchone()
                
                # Temps total d'écoute (dernières 24h)
                cursor = conn.execute("""
                    SELECT SUM(duration_seconds) FROM communication_logs 
                    WHERE timestamp >= ?
                """, (since_time.isoformat(),))
                total_duration = cursor.fetchone()[0] or 0
                
                return {
                    "total_presets": total_presets,
                    "recent_communications": recent_comms,
                    "most_used_frequency": {
                        "name": most_used[0] if most_used else None,
                        "frequency": most_used[1] if most_used else None,
                        "use_count": most_used[2] if most_used else 0
                    },
                    "total_listening_time_hours": round(total_duration / 3600, 2)
                }
                
        except Exception as e:
            self.logger.error(f"❌ Erreur calcul statistiques: {e}")
            return {}
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """Nettoie les anciens logs"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with sqlite3.connect(self.db_path) as conn:
                # Nettoyer les logs de communication
                cursor = conn.execute("""
                    DELETE FROM communication_logs 
                    WHERE timestamp < ?
                """, (cutoff_date.isoformat(),))
                comm_deleted = cursor.rowcount
                
                # Nettoyer les logs système
                cursor = conn.execute("""
                    DELETE FROM system_logs 
                    WHERE timestamp < ?
                """, (cutoff_date.isoformat(),))
                sys_deleted = cursor.rowcount
                
                conn.commit()
                
                self.logger.info(f"🧹 Nettoyage: {comm_deleted} logs comm, {sys_deleted} logs système supprimés")
                
        except Exception as e:
            self.logger.error(f"❌ Erreur nettoyage: {e}")
