"""
Gestionnaire WebSocket pour communication temps réel ICOM R8600
Synchronisation bidirectionnelle et notifications instantanées
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional, Callable
from fastapi import WebSocket, WebSocketDisconnect
from dataclasses import dataclass, asdict
from enum import Enum

class MessageType(Enum):
    STATUS_UPDATE = "status_update"
    COMMAND_RESULT = "command_result"
    FREQUENCY_CHANGE = "frequency_change"
    MODE_CHANGE = "mode_change"
    SIGNAL_UPDATE = "signal_update"
    CONNECTION_STATUS = "connection_status"
    ERROR = "error"
    NOTIFICATION = "notification"
    HEARTBEAT = "heartbeat"

@dataclass
class WebSocketMessage:
    type: str
    data: Dict[str, Any]
    timestamp: float = None
    client_id: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class WebSocketManager:
    """Gestionnaire WebSocket pour communication temps réel"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.client_info: Dict[str, Dict[str, Any]] = {}
        self.message_handlers: Dict[str, Callable] = {}
        self.heartbeat_interval = 30  # secondes
        self.logger = logging.getLogger(__name__)
        
        # Statistiques
        self.stats = {
            "total_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0
        }
        
        # Démarrer le heartbeat
        asyncio.create_task(self._heartbeat_worker())
    
    async def connect(self, websocket: WebSocket, client_id: str = None) -> str:
        """Accepte une nouvelle connexion WebSocket"""
        try:
            await websocket.accept()
            
            # Générer un ID client si non fourni
            if not client_id:
                client_id = f"client_{int(time.time() * 1000)}"
            
            # Stocker la connexion
            self.active_connections[client_id] = websocket
            self.client_info[client_id] = {
                "connected_at": time.time(),
                "last_heartbeat": time.time(),
                "ip": websocket.client.host if websocket.client else "unknown"
            }
            
            self.stats["total_connections"] += 1
            
            # Envoyer message de bienvenue
            await self.send_to_client(client_id, MessageType.CONNECTION_STATUS, {
                "status": "connected",
                "client_id": client_id,
                "server_time": time.time()
            })
            
            self.logger.info(f"🔌 Client connecté: {client_id}")
            
            # Notifier les autres clients
            await self.broadcast(MessageType.NOTIFICATION, {
                "message": f"Nouveau client connecté: {client_id}",
                "type": "client_connected"
            }, exclude_client=client_id)
            
            return client_id
            
        except Exception as e:
            self.logger.error(f"❌ Erreur connexion WebSocket: {e}")
            raise
    
    async def disconnect(self, client_id: str):
        """Déconnecte un client"""
        try:
            if client_id in self.active_connections:
                # Fermer la connexion
                websocket = self.active_connections[client_id]
                try:
                    await websocket.close()
                except:
                    pass  # Connexion peut déjà être fermée
                
                # Nettoyer les données
                del self.active_connections[client_id]
                del self.client_info[client_id]
                
                self.logger.info(f"🔌 Client déconnecté: {client_id}")
                
                # Notifier les autres clients
                await self.broadcast(MessageType.NOTIFICATION, {
                    "message": f"Client déconnecté: {client_id}",
                    "type": "client_disconnected"
                })
                
        except Exception as e:
            self.logger.error(f"❌ Erreur déconnexion: {e}")
    
    async def send_to_client(self, client_id: str, message_type: MessageType, data: Dict[str, Any]) -> bool:
        """Envoie un message à un client spécifique"""
        try:
            if client_id not in self.active_connections:
                return False
            
            websocket = self.active_connections[client_id]
            message = WebSocketMessage(
                type=message_type.value,
                data=data,
                client_id=client_id
            )
            
            await websocket.send_text(json.dumps(asdict(message)))
            self.stats["messages_sent"] += 1
            
            return True
            
        except WebSocketDisconnect:
            await self.disconnect(client_id)
            return False
        except Exception as e:
            self.logger.error(f"❌ Erreur envoi message à {client_id}: {e}")
            self.stats["errors"] += 1
            return False
    
    async def broadcast(self, message_type: MessageType, data: Dict[str, Any], exclude_client: str = None):
        """Diffuse un message à tous les clients connectés"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            if exclude_client and client_id == exclude_client:
                continue
            
            try:
                message = WebSocketMessage(
                    type=message_type.value,
                    data=data
                )
                
                await websocket.send_text(json.dumps(asdict(message)))
                self.stats["messages_sent"] += 1
                
            except WebSocketDisconnect:
                disconnected_clients.append(client_id)
            except Exception as e:
                self.logger.error(f"❌ Erreur broadcast à {client_id}: {e}")
                disconnected_clients.append(client_id)
                self.stats["errors"] += 1
        
        # Nettoyer les clients déconnectés
        for client_id in disconnected_clients:
            await self.disconnect(client_id)
    
    async def handle_message(self, client_id: str, message: str):
        """Traite un message reçu d'un client"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            message_data = data.get("data", {})
            
            self.stats["messages_received"] += 1
            
            # Mettre à jour le heartbeat
            if client_id in self.client_info:
                self.client_info[client_id]["last_heartbeat"] = time.time()
            
            # Traiter selon le type de message
            if message_type == "heartbeat":
                await self.send_to_client(client_id, MessageType.HEARTBEAT, {
                    "server_time": time.time()
                })
            
            elif message_type == "command":
                # Déléguer aux handlers de commandes
                await self._handle_command(client_id, message_data)
            
            elif message_type == "subscribe":
                # Gestion des abonnements aux notifications
                await self._handle_subscription(client_id, message_data)
            
            else:
                # Handler personnalisé
                if message_type in self.message_handlers:
                    await self.message_handlers[message_type](client_id, message_data)
                else:
                    self.logger.warning(f"⚠️ Type de message inconnu: {message_type}")
            
        except json.JSONDecodeError:
            await self.send_to_client(client_id, MessageType.ERROR, {
                "error": "Format JSON invalide"
            })
        except Exception as e:
            self.logger.error(f"❌ Erreur traitement message: {e}")
            await self.send_to_client(client_id, MessageType.ERROR, {
                "error": str(e)
            })
    
    async def _handle_command(self, client_id: str, command_data: Dict[str, Any]):
        """Traite une commande reçue via WebSocket"""
        command = command_data.get("command")
        params = command_data.get("params", {})
        
        # Ici, on déléguerait à l'handler ICOM
        # Pour l'exemple, on simule une réponse
        await self.send_to_client(client_id, MessageType.COMMAND_RESULT, {
            "command": command,
            "success": True,
            "result": f"Commande {command} exécutée"
        })
    
    async def _handle_subscription(self, client_id: str, sub_data: Dict[str, Any]):
        """Gère les abonnements aux notifications"""
        # Implémenter la logique d'abonnement
        pass
    
    def register_handler(self, message_type: str, handler: Callable):
        """Enregistre un handler pour un type de message"""
        self.message_handlers[message_type] = handler
    
    async def _heartbeat_worker(self):
        """Worker pour vérifier les connexions actives"""
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                current_time = time.time()
                timeout_threshold = current_time - (self.heartbeat_interval * 2)
                
                # Vérifier les clients inactifs
                inactive_clients = []
                for client_id, info in self.client_info.items():
                    if info["last_heartbeat"] < timeout_threshold:
                        inactive_clients.append(client_id)
                
                # Déconnecter les clients inactifs
                for client_id in inactive_clients:
                    self.logger.warning(f"⏱️ Client inactif déconnecté: {client_id}")
                    await self.disconnect(client_id)
                
                # Envoyer heartbeat aux clients actifs
                await self.broadcast(MessageType.HEARTBEAT, {
                    "server_time": current_time,
                    "active_clients": len(self.active_connections)
                })
                
            except Exception as e:
                self.logger.error(f"❌ Erreur heartbeat worker: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques WebSocket"""
        return {
            **self.stats,
            "active_connections": len(self.active_connections),
            "client_list": list(self.active_connections.keys())
        }
    
    async def broadcast_radio_status(self, status: Dict[str, Any]):
        """Diffuse le statut du radio à tous les clients"""
        await self.broadcast(MessageType.STATUS_UPDATE, status)
    
    async def broadcast_frequency_change(self, frequency: int, mode: str):
        """Diffuse un changement de fréquence"""
        await self.broadcast(MessageType.FREQUENCY_CHANGE, {
            "frequency": frequency,
            "mode": mode,
            "timestamp": time.time()
        })
    
    async def broadcast_signal_update(self, rssi: int, frequency: int):
        """Diffuse une mise à jour du signal"""
        await self.broadcast(MessageType.SIGNAL_UPDATE, {
            "rssi": rssi,
            "frequency": frequency,
            "timestamp": time.time()
        })
    
    async def broadcast_error(self, error_message: str, error_type: str = "general"):
        """Diffuse une erreur à tous les clients"""
        await self.broadcast(MessageType.ERROR, {
            "message": error_message,
            "type": error_type,
            "timestamp": time.time()
        })

# Instance globale du gestionnaire WebSocket
websocket_manager = WebSocketManager()

# Fonctions utilitaires pour l'intégration FastAPI
async def websocket_endpoint(websocket: WebSocket, client_id: str = None):
    """Point d'entrée WebSocket pour FastAPI"""
    client_id = await websocket_manager.connect(websocket, client_id)
    
    try:
        while True:
            message = await websocket.receive_text()
            await websocket_manager.handle_message(client_id, message)
            
    except WebSocketDisconnect:
        await websocket_manager.disconnect(client_id)
    except Exception as e:
        logging.error(f"❌ Erreur WebSocket: {e}")
        await websocket_manager.disconnect(client_id)
