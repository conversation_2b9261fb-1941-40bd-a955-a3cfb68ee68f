"""
Handler réseau ultra-rapide pour ICOM IC-R8600
Optimisé pour latence < 50ms et communication temps réel
"""

import socket
import asyncio
import time
import logging
import struct
from typing import Optional, Dict, Any, Callable, List
from dataclasses import dataclass
from enum import Enum
import threading
from concurrent.futures import ThreadPoolExecutor

class Protocol(Enum):
    TCP = "tcp"
    UDP = "udp"

@dataclass
class UltraFastConfig:
    """Configuration ultra-rapide pour IC-R8600"""
    host: str = "*************"
    port: int = 50001
    protocol: Protocol = Protocol.UDP  # UDP plus rapide pour commandes
    timeout: float = 0.02  # 20ms timeout ultra-court
    max_concurrent: int = 10
    buffer_size: int = 4096
    keepalive_interval: float = 0.5
    command_queue_size: int = 100

class UltraFastICOMHandler:
    """Handler ultra-rapide pour IC-R8600 avec latence < 50ms"""
    
    def __init__(self, config: UltraFastConfig = None, status_callback: Callable = None):
        self.config = config or UltraFastConfig()
        self.status_callback = status_callback
        self.socket = None
        self.connected = False
        self.command_queue = asyncio.Queue(maxsize=self.config.command_queue_size)
        self.response_futures = {}
        self.command_id = 0
        self.stats = {
            "commands_sent": 0,
            "responses_received": 0,
            "avg_latency_ms": 0,
            "max_latency_ms": 0,
            "errors": 0
        }
        
        self.logger = logging.getLogger(__name__)
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_concurrent)
        
    async def connect(self) -> bool:
        """Connexion ultra-rapide"""
        try:
            if self.config.protocol == Protocol.UDP:
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                # Optimisations UDP
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, self.config.buffer_size)
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, self.config.buffer_size)
            else:
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                # Optimisations TCP
                self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                await asyncio.get_event_loop().run_in_executor(
                    None, self.socket.connect, (self.config.host, self.config.port)
                )
            
            self.socket.settimeout(self.config.timeout)
            
            # Test de connexion ultra-rapide
            test_success = await self._test_connection()
            if test_success:
                self.connected = True
                self.logger.info(f"🚀 Connexion ultra-rapide établie ({self.config.protocol.value.upper()})")
                # Démarrer les workers asynchrones
                asyncio.create_task(self._command_worker())
                asyncio.create_task(self._response_worker())
                return True
            else:
                self.logger.warning("⚠️ Test connexion échoué, mode simulation")
                self.connected = True  # Mode simulation
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Erreur connexion ultra-rapide: {e}")
            return False
    
    async def _test_connection(self) -> bool:
        """Test de connexion ultra-rapide"""
        try:
            # Ping CI-V ultra-rapide
            ping_cmd = bytes([0xFE, 0xFE, 0x96, 0xE0, 0xFD])
            start_time = time.time()
            
            if self.config.protocol == Protocol.UDP:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.socket.sendto, ping_cmd, (self.config.host, self.config.port)
                )
                try:
                    response, _ = await asyncio.get_event_loop().run_in_executor(
                        None, self.socket.recvfrom, 1024
                    )
                    latency = (time.time() - start_time) * 1000
                    self.logger.info(f"🏓 Ping réussi: {latency:.1f}ms")
                    return True
                except socket.timeout:
                    self.logger.warning("⏱️ Timeout ping")
                    return False
            else:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.socket.send, ping_cmd
                )
                response = await asyncio.get_event_loop().run_in_executor(
                    None, self.socket.recv, 1024
                )
                latency = (time.time() - start_time) * 1000
                self.logger.info(f"🏓 Ping réussi: {latency:.1f}ms")
                return True
                
        except Exception as e:
            self.logger.warning(f"Test connexion échoué: {e}")
            return False
    
    async def send_command_fast(self, command: bytes) -> Optional[bytes]:
        """Envoie une commande avec latence ultra-faible"""
        if not self.connected:
            return None
        
        try:
            command_id = self.command_id
            self.command_id += 1
            
            # Créer un Future pour la réponse
            response_future = asyncio.Future()
            self.response_futures[command_id] = response_future
            
            # Ajouter à la queue de commandes
            await self.command_queue.put((command_id, command))
            
            # Attendre la réponse avec timeout
            try:
                response = await asyncio.wait_for(response_future, timeout=self.config.timeout)
                return response
            except asyncio.TimeoutError:
                self.logger.warning(f"⏱️ Timeout commande {command_id}")
                self.stats["errors"] += 1
                return None
            finally:
                # Nettoyer le future
                self.response_futures.pop(command_id, None)
                
        except Exception as e:
            self.logger.error(f"❌ Erreur send_command_fast: {e}")
            self.stats["errors"] += 1
            return None
    
    async def _command_worker(self):
        """Worker asynchrone pour traitement des commandes"""
        while self.connected:
            try:
                command_id, command = await self.command_queue.get()
                start_time = time.time()
                
                # Envoyer la commande
                if self.config.protocol == Protocol.UDP:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.socket.sendto, command, (self.config.host, self.config.port)
                    )
                else:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.socket.send, command
                    )
                
                self.stats["commands_sent"] += 1
                
                # Marquer la tâche comme terminée
                self.command_queue.task_done()
                
            except Exception as e:
                self.logger.error(f"❌ Erreur command_worker: {e}")
                await asyncio.sleep(0.001)  # Pause courte en cas d'erreur
    
    async def _response_worker(self):
        """Worker asynchrone pour traitement des réponses"""
        while self.connected:
            try:
                # Lire les réponses en continu
                if self.config.protocol == Protocol.UDP:
                    response, _ = await asyncio.get_event_loop().run_in_executor(
                        None, self.socket.recvfrom, self.config.buffer_size
                    )
                else:
                    response = await asyncio.get_event_loop().run_in_executor(
                        None, self.socket.recv, self.config.buffer_size
                    )
                
                if response:
                    self.stats["responses_received"] += 1
                    # Traiter la réponse (simplifiée pour l'exemple)
                    await self._process_response(response)
                
            except socket.timeout:
                continue  # Timeout normal, continuer
            except Exception as e:
                self.logger.error(f"❌ Erreur response_worker: {e}")
                await asyncio.sleep(0.001)
    
    async def _process_response(self, response: bytes):
        """Traite une réponse reçue"""
        try:
            # Logique simplifiée - associer la réponse au bon Future
            # Dans une implémentation complète, il faudrait parser le protocole CI-V
            if self.response_futures:
                # Prendre le plus ancien Future en attente
                command_id = next(iter(self.response_futures))
                future = self.response_futures.get(command_id)
                if future and not future.done():
                    future.set_result(response)
        except Exception as e:
            self.logger.error(f"❌ Erreur process_response: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques de performance"""
        return {
            **self.stats,
            "connected": self.connected,
            "queue_size": self.command_queue.qsize(),
            "pending_responses": len(self.response_futures)
        }
    
    async def disconnect(self):
        """Déconnexion propre"""
        self.connected = False
        if self.socket:
            self.socket.close()
        self.executor.shutdown(wait=False)
        self.logger.info("🔌 Déconnexion ultra-rapide")

# Fonctions utilitaires pour commandes CI-V
def build_frequency_command(freq_hz: int) -> bytes:
    """Construit une commande de changement de fréquence"""
    # Conversion en BCD pour IC-R8600
    freq_str = f"{freq_hz:010d}"
    bcd_bytes = []
    for i in range(0, 10, 2):
        byte_val = int(freq_str[i+1]) << 4 | int(freq_str[i])
        bcd_bytes.append(byte_val)
    
    # Commande CI-V: FE FE 96 E0 05 [freq_bcd] FD
    cmd = [0xFE, 0xFE, 0x96, 0xE0, 0x05] + bcd_bytes + [0xFD]
    return bytes(cmd)

def build_mode_command(mode: str) -> bytes:
    """Construit une commande de changement de mode"""
    mode_map = {
        'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
        'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08
    }
    mode_byte = mode_map.get(mode, 0x05)  # FM par défaut
    
    # Commande CI-V: FE FE 96 E0 06 [mode] FD
    cmd = [0xFE, 0xFE, 0x96, 0xE0, 0x06, mode_byte, 0xFD]
    return bytes(cmd)

def build_power_command(power_on: bool) -> bytes:
    """Construit une commande d'alimentation"""
    power_byte = 0x01 if power_on else 0x00
    
    # Commande CI-V: FE FE 96 E0 18 [power] FD
    cmd = [0xFE, 0xFE, 0x96, 0xE0, 0x18, power_byte, 0xFD]
    return bytes(cmd)
